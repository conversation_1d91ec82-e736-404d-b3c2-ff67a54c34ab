<?php

declare(strict_types=1);

namespace app\command;

use app\common\library\MeiliSearch;
use think\console\Command;
use think\console\Input;
use think\console\input\Option;
use think\console\Output;

/**
 * 配置 MeiliSearch 索引设置
 */
class ConfigureMeiliSearchIndex extends Command
{
    protected function configure()
    {
        // 指令配置
        $this->setName('configure_meilisearch_index')
            ->addOption('index', 'i', Option::VALUE_OPTIONAL, '索引名称', 'goods')
            ->addOption('reset', 'r', Option::VALUE_NONE, '重置索引（删除后重新创建）')
            ->addOption('debug', 'd', Option::VALUE_NONE, '开启调试模式')
            ->setDescription('配置 MeiliSearch 索引设置');
    }

    protected function execute(Input $input, Output $output)
    {
        $indexName = $input->getOption('index');
        $reset = $input->getOption('reset');
        $debug = $input->getOption('debug');

        $output->writeln("开始配置 MeiliSearch 索引: {$indexName}");

        // 初始化 MeiliSearch
        $meili = new MeiliSearch();
        
        // 测试连接
        if (!$meili->testConnection()) {
            $output->writeln("<error>MeiliSearch 连接失败</error>");
            return Command::FAILURE;
        }

        $output->writeln("MeiliSearch 连接成功");

        // 如果需要重置索引
        if ($reset) {
            $output->writeln("正在重置索引...");
            try {
                $result = $meili->deleteIndex($indexName);
                if ($debug) {
                    $output->writeln("删除索引结果: " . json_encode($result));
                }
                sleep(2); // 等待删除完成
                $output->writeln("索引已删除");
            } catch (\Exception $e) {
                $output->writeln("<error>删除索引失败: " . $e->getMessage() . "</error>");
            }
        }

        // 检查索引是否存在
        $indexExists = false;
        try {
            $meili->getIndex($indexName);
            $indexExists = true;
            $output->writeln("索引已存在");
        } catch (\Exception $e) {
            $output->writeln("索引不存在，将创建新索引");
        }

        // 如果索引不存在，创建它
        if (!$indexExists) {
            $output->writeln("正在创建索引...");
            try {
                $result = $meili->createIndex($indexName, ['primaryKey' => 'id']);
                if ($debug) {
                    $output->writeln("创建索引结果: " . json_encode($result));
                }
                sleep(3); // 等待创建完成
                $output->writeln("索引创建成功");
            } catch (\Exception $e) {
                $output->writeln("<error>创建索引失败: " . $e->getMessage() . "</error>");
                return Command::FAILURE;
            }
        }

        // 配置索引设置
        $output->writeln("正在配置索引设置...");
        
        $settings = [
            'searchableAttributes' => [
                'name',
                'remark',
                'content',
                'category_path',
                'brand_name',
                'tags'
            ],
            'filterableAttributes' => [
                'del',
                'status',
                'audit_status',
                'shop_id',
                'first_cate_id',
                'second_cate_id',
                'third_cate_id',
                'brand_id',
                'join_jc',
                'goods_label',
                'goods_label_top',
                'is_hot',
                'is_recommend',
                'min_price',
                'market_price'
            ],
            'sortableAttributes' => [
                'sales_total',
                'sales_actual',
                'sales_virtual',
                'min_price',
                'market_price',
                'create_time',
                'update_time',
                'sort_weight',
                'clicks',
                'clicks_virtual'
            ],
            'rankingRules' => [
                'words',
                'typo',
                'proximity',
                'attribute',
                'sort',
                'exactness'
            ]
        ];

        try {
            $result = $meili->updateIndexSettings($indexName, $settings);
            if ($debug) {
                $output->writeln("配置结果: " . json_encode($result));
            }
            
            $output->writeln("索引设置配置成功！");
            $output->writeln("可搜索属性: " . implode(', ', $settings['searchableAttributes']));
            $output->writeln("可过滤属性: " . implode(', ', $settings['filterableAttributes']));
            $output->writeln("可排序属性: " . implode(', ', $settings['sortableAttributes']));
            
            // 等待设置生效
            $output->writeln("等待设置生效...");
            sleep(3);
            $output->writeln("配置完成！");
            
            return Command::SUCCESS;
            
        } catch (\Exception $e) {
            $output->writeln("<error>配置索引设置失败: " . $e->getMessage() . "</error>");
            return Command::FAILURE;
        }
    }
}
