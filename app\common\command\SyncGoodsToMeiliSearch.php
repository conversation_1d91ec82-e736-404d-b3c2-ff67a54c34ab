<?php

namespace app\common\command;

use app\common\library\MeiliSearch;
use app\common\model\goods\Goods;
use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Db;
use think\facade\Log;

/**
 * 同步商品数据到MeiliSearch
 */
class SyncGoodsToMeiliSearch extends Command
{
    protected function configure()
    {
        $this->setName('sync_goods_to_meilisearch')
            ->setDescription('同步商品数据到MeiliSearch');
    }

    protected function execute(Input $input, Output $output)
    {
        try {
            $output->writeln('开始同步商品数据到MeiliSearch...');

            // 初始化MeiliSearch客户端
            $meili = new MeiliSearch();
            $output->writeln('删除旧索引...');
            $meili->deleteIndex('goods');
            // 获取索引信息
            $indexInfo = $meili->getIndex('goods');
           
            if (!$indexInfo || isset($indexInfo['error'])) {
                $output->writeln('正在创建索引...');
                // 索引不存在，创建索引
                $settings = [
                    'searchableAttributes' => [
                        'name',
                        'remark',
                        'split_word',
                        'content',
                        
                    ],
                    'filterableAttributes' => [
                        'shop_id',
                        'first_cate_id',
                        'second_cate_id',
                        'third_cate_id',
                        'brand_id',
                        'status',
                        'is_hot',
                        'del',
                        'audit_status',
                        'join_jc',
                        'is_recommend'
                    ],
                    'sortableAttributes' => [
                        'min_price',
                        'sales_actual',
                        'sales_virtual',
                        'create_time',
                        'update_time'
                    ],
                    'rankingRules' => [
                        'words',
                        'typo',
                        'proximity',
                        'attribute',
                        'sort',
                        'exactness'
                    ],
                    'synonyms' => [
                        '手机' => ['phone', '智能机', '手机壳', '手机膜'],
                        '电脑' => ['笔记本', '台式机', 'PC', '电脑包'],
                        '衣服' => ['服装', '衣物', '衣裳', '服饰'],
                        '鞋子' => ['鞋', '鞋靴', '靴子', '运动鞋'],
                        '包包' => ['包', '手提包', '背包', '钱包'],
                        '眼镜' => ['老花镜', '太阳镜', '墨镜', '近视镜']
                    ]
                ];
                $meili->createIndex('goods', $settings);
                $output->writeln('索引已创建');
            } else {
                $output->writeln('索引已存在，继续同步数据');
            }

            // 获取所有上架的商品
            $total = Goods::where([
                ['del', '=', 0],
                ['status', '=', 1],
                ['audit_status', '=', 1]
            ])->count();

            $output->writeln("共有 {$total} 个商品需要同步");

            // 分批处理，每批500个
            $batchSize = 500;
            $success = 0;
            $error = 0;
            $page = 1;

            while (true) {
                // 使用page方法代替offset方法
                $goods = Goods::where([
                    ['del', '=', 0],
                    ['status', '=', 1],
                    ['audit_status', '=', 1]
                ])
                    ->field('id, name, image,remark,del,status,audit_status,join_jc,content, split_word, shop_id, first_cate_id, second_cate_id, third_cate_id, brand_id, min_price, market_price, sales_actual, sales_virtual, is_hot, is_recommend, create_time, update_time') // Ensure split_word is fetched
                    ->page($page, $batchSize)
                    ->select()
                    ->toArray();

                if (empty($goods)) {
                    break;
                }

                $startItem = ($page - 1) * $batchSize + 1;
                $endItem = $startItem + count($goods) - 1;
                $output->writeln("正在处理第 {$startItem} 到 {$endItem} 个商品");

                try {
                    // 导入数据到MeiliSearch
                    $output->writeln("正在导入数据到MeiliSearch...");

                    // 确保数据格式正确
                    foreach ($goods as &$item) {
                        // 确保所有字段都是字符串或数字, 并将 split_word 添加到要导入的文档中
                    $documentsToIndex = [];
                    foreach ($goods as $item) {
                        $doc = [];
                        foreach ($item as $key => $value) {
                            if (is_array($value)) {
                                $doc[$key] = json_encode($value);
                            } else {
                                $doc[$key] = $value;
                            }
                        }
                        // 确保 split_word 字段存在于文档中
                        $doc['split_word'] = $item['split_word'] ?? ''; 
                        $documentsToIndex[] = $doc;
                    }
                }

                $response = $meili->importDocuments('goods', $documentsToIndex);

                    if (isset($response['taskUid'])) {
                        $success += count($goods);
                        $output->writeln("成功导入 " . count($goods) . " 个商品，任务ID：{$response['taskUid']}");
                    } else {
                        $error += count($goods);
                        $output->writeln("导入失败：" . json_encode($response));
                        // 记录详细错误信息
                        Log::write('MeiliSearch导入失败: ' . json_encode($response));
                    }
                } catch (\Exception $e) {
                    $error += count($goods);
                    $output->writeln("导入异常：" . $e->getMessage());
                    Log::write('MeiliSearch导入异常: ' . $e->getMessage());
                }

                // 增加页码，继续下一页
                $page++;
            }

            $output->writeln("同步完成，总共 {$total} 个商品，成功 {$success} 个，失败 {$error} 个");
            return true;
        } catch (\Exception $e) {
            Log::write('同步商品数据到MeiliSearch异常: ' . $e->getMessage());
            $output->writeln('同步商品数据到MeiliSearch异常: ' . $e->getMessage());
            return false;
        }
    }
}
