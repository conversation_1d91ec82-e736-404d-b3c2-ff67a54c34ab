<?php
/**
 * 快速修复 MeiliSearch 索引配置脚本
 * 解决 "Attribute `del` is not filterable" 错误
 */

// 设置基础路径
define('ROOT_PATH', __DIR__ . '/../');

// 加载 ThinkPHP 框架
require_once ROOT_PATH . 'vendor/autoload.php';

// 初始化应用
$app = new \think\App();
$app->initialize();

// 现在可以使用 MeiliSearch 类
use app\common\library\MeiliSearch;

echo "开始修复 MeiliSearch 索引配置...\n";

try {
    // 初始化 MeiliSearch
    $meili = new MeiliSearch();
    
    // 测试连接
    if (!$meili->testConnection()) {
        echo "错误: MeiliSearch 连接失败\n";
        exit(1);
    }
    
    echo "MeiliSearch 连接成功\n";
    
    // 配置索引设置
    $indexName = 'goods';
    echo "正在配置索引 '{$indexName}' 的设置...\n";
    
    $settings = [
        'searchableAttributes' => [
            'name',
            'remark',
            'content',
            'category_path',
            'brand_name',
            'tags'
        ],
        'filterableAttributes' => [
            'del',
            'status',
            'audit_status',
            'shop_id',
            'first_cate_id',
            'second_cate_id',
            'third_cate_id',
            'brand_id',
            'join_jc',
            'goods_label',
            'goods_label_top',
            'is_hot',
            'is_recommend',
            'min_price',
            'market_price'
        ],
        'sortableAttributes' => [
            'sales_total',
            'sales_actual',
            'sales_virtual',
            'min_price',
            'market_price',
            'create_time',
            'update_time',
            'sort_weight',
            'clicks',
            'clicks_virtual'
        ],
        'rankingRules' => [
            'words',
            'typo',
            'proximity',
            'attribute',
            'sort',
            'exactness'
        ]
    ];
    
    // 分别更新各个设置，避免批量设置失败
    echo "正在设置可搜索属性...\n";
    $result1 = $meili->updateSearchableAttributes($indexName, $settings['searchableAttributes']);
    if (isset($result1['error'])) {
        echo "设置可搜索属性失败: " . $result1['error'] . "\n";
    } else {
        echo "可搜索属性设置成功，任务ID: " . ($result1['taskUid'] ?? 'N/A') . "\n";
    }
    sleep(1);

    echo "正在设置可过滤属性...\n";
    $result2 = $meili->updateFilterableAttributes($indexName, $settings['filterableAttributes']);
    if (isset($result2['error'])) {
        echo "设置可过滤属性失败: " . $result2['error'] . "\n";
    } else {
        echo "可过滤属性设置成功，任务ID: " . ($result2['taskUid'] ?? 'N/A') . "\n";
    }
    sleep(1);

    echo "正在设置可排序属性...\n";
    $result3 = $meili->updateSortableAttributes($indexName, $settings['sortableAttributes']);
    if (isset($result3['error'])) {
        echo "设置可排序属性失败: " . $result3['error'] . "\n";
    } else {
        echo "可排序属性设置成功，任务ID: " . ($result3['taskUid'] ?? 'N/A') . "\n";
    }

    echo "索引设置更新完成！\n";
    
    echo "配置详情:\n";
    echo "- 可搜索属性: " . implode(', ', $settings['searchableAttributes']) . "\n";
    echo "- 可过滤属性: " . implode(', ', $settings['filterableAttributes']) . "\n";
    echo "- 可排序属性: " . implode(', ', $settings['sortableAttributes']) . "\n";
    
    echo "\n等待设置生效（5秒）...\n";
    sleep(5);
    
    echo "修复完成！现在可以正常使用商品搜索功能了。\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    exit(1);
}
