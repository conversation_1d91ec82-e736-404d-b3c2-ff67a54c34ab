# MeiliSearch 索引配置建议

## 概述

为了支持商品搜索的 MeiliSearch 优先策略和个性化推荐算法，需要正确配置 MeiliSearch 索引的各项属性。

## 索引名称

建议使用索引名称：`goods`

## 必需的索引配置

### 1. 可搜索属性 (searchableAttributes)

```json
[
  "name",
  "remark", 
  "content",
  "category_path",
  "brand_name",
  "tags"
]
```

**说明：**
- `name`: 商品名称（最高权重）
- `remark`: 商品描述
- `content`: 商品详情内容
- `category_path`: 分类路径（数组格式，如 ["电子产品", "手机", "智能手机"]）
- `brand_name`: 品牌名称
- `tags`: 商品标签（数组格式）

### 2. 可过滤属性 (filterableAttributes)

```json
[
  "del",
  "status", 
  "audit_status",
  "shop_id",
  "first_cate_id",
  "second_cate_id", 
  "third_cate_id",
  "brand_id",
  "join_jc",
  "goods_label",
  "goods_label_top",
  "is_hot",
  "min_price",
  "market_price"
]
```

**说明：**
- `del`: 删除状态（0=正常，1=已删除）
- `status`: 商品状态（1=上架，0=下架）
- `audit_status`: 审核状态（1=通过，0=待审核，2=拒绝）
- `shop_id`: 店铺ID
- `first_cate_id`, `second_cate_id`, `third_cate_id`: 一、二、三级分类ID
- `brand_id`: 品牌ID
- `join_jc`: 是否参与拼单集采（0=否，1=是）
- `goods_label`, `goods_label_top`: 商品标签
- `is_hot`: 是否热门商品
- `min_price`, `market_price`: 价格范围过滤

### 3. 可排序属性 (sortableAttributes)

```json
[
  "sales_total",
  "sales_actual",
  "sales_virtual", 
  "min_price",
  "market_price",
  "create_time",
  "sort_weight",
  "clicks",
  "clicks_virtual"
]
```

**说明：**
- `sales_total`: 总销量（实际销量+虚拟销量）
- `sales_actual`: 实际销量
- `sales_virtual`: 虚拟销量
- `min_price`: 最低价格
- `market_price`: 市场价格
- `create_time`: 创建时间（用于新品排序）
- `sort_weight`: 商品权重（数字越小权重越大）
- `clicks`: 实际点击量
- `clicks_virtual`: 虚拟点击量

### 4. 显示属性 (displayedAttributes)

```json
[
  "id",
  "name",
  "image", 
  "remark",
  "min_price",
  "market_price",
  "sales_actual",
  "sales_virtual",
  "sales_total",
  "first_cate_id",
  "second_cate_id",
  "third_cate_id",
  "sort_weight",
  "brand_id",
  "shop_id",
  "is_hot",
  "goods_label",
  "goods_label_top",
  "join_jc",
  "year_jc_sales",
  "year_sales",
  "video",
  "category_path",
  "brand_name",
  "tags",
  "content",
  "clicks",
  "clicks_virtual",
  "create_time"
]
```

## 高级配置

### 1. 同义词 (synonyms)

```json
{
  "轮椅": ["轮椅车", "轮椅", "残疾人轮椅", "老人轮椅"],
  "手机": ["手机", "智能手机", "移动电话"],
  "老人": ["老年人", "长者", "老人家", "老人"],
  "护理": ["护理用品", "护理", "照护", "看护"]
}
```

### 2. 停用词 (stopWords)

```json
["的", "了", "在", "是", "我", "有", "和", "就", "不", "人", "都", "一", "一个", "上", "也", "很", "到", "说", "要", "去", "你", "会", "着", "没有", "看", "好", "自己", "这"]
```

### 3. 排序规则 (rankingRules)

```json
[
  "words",
  "typo", 
  "proximity",
  "attribute",
  "sort",
  "exactness"
]
```

## 数据同步建议

### 1. 字段映射

确保数据库字段正确映射到 MeiliSearch 文档：

```php
// 示例数据结构
$document = [
    'id' => $goods['id'],
    'name' => $goods['name'],
    'remark' => $goods['remark'],
    'content' => strip_tags($goods['content']), // 移除HTML标签
    'category_path' => [
        $firstCategoryName,
        $secondCategoryName, 
        $thirdCategoryName
    ],
    'brand_name' => $brandName,
    'tags' => explode(',', $goods['tags']), // 转换为数组
    'sales_total' => $goods['sales_actual'] + $goods['sales_virtual'],
    // ... 其他字段
];
```

### 2. 增量同步

建议实现增量同步机制：
- 商品创建/更新时同步到 MeiliSearch
- 定期全量同步（如每日凌晨）
- 监控同步状态和错误

## 性能优化建议

1. **索引大小控制**: 定期清理已删除商品的索引数据
2. **分页优化**: 使用 `limit` 和 `offset` 进行分页，避免大结果集
3. **缓存策略**: 对热门搜索词结果进行缓存
4. **监控指标**: 监控搜索响应时间、索引大小、同步延迟等

## 配置示例脚本

```php
// MeiliSearch 索引配置脚本示例
$meili = new \app\common\library\MeiliSearch();

// 创建索引
$meili->createIndex('goods', ['primaryKey' => 'id']);

// 配置可搜索属性
$meili->updateSearchableAttributes('goods', [
    'name', 'remark', 'content', 'category_path', 'brand_name', 'tags'
]);

// 配置可过滤属性  
$meili->updateFilterableAttributes('goods', [
    'del', 'status', 'audit_status', 'shop_id', 'first_cate_id', 
    'second_cate_id', 'third_cate_id', 'brand_id', 'join_jc',
    'goods_label', 'goods_label_top', 'is_hot', 'min_price', 'market_price'
]);

// 配置可排序属性
$meili->updateSortableAttributes('goods', [
    'sales_total', 'sales_actual', 'sales_virtual', 'min_price', 
    'market_price', 'create_time', 'sort_weight', 'clicks', 'clicks_virtual'
]);
```

## 注意事项

1. **数据类型**: 确保数值字段在 MeiliSearch 中为数值类型，字符串字段为字符串类型
2. **数组字段**: `category_path` 和 `tags` 应为数组格式，便于搜索和过滤
3. **空值处理**: 处理空值和 null 值，避免索引错误
4. **字符编码**: 确保所有文本数据使用 UTF-8 编码
5. **索引大小**: 监控索引大小，避免过大影响性能

## 测试验证

配置完成后，建议进行以下测试：

1. **基础搜索**: 测试商品名称、描述等基础搜索功能
2. **过滤搜索**: 测试分类、品牌、价格等过滤功能  
3. **排序功能**: 测试按价格、销量、时间等排序
4. **高亮功能**: 验证搜索结果高亮显示
5. **性能测试**: 测试大数据量下的搜索性能
