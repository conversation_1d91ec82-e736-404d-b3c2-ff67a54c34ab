<?php


namespace app\api\logic;

use app\common\service\AdminNotificationService;
use app\common\model\distribution\Distribution;
use app\common\model\distribution\DistributionLevel;
use app\common\model\goods\GoodsItem;
use app\common\model\ProductWord;
use app\common\model\shop\ShopFollow;
use app\common\model\user\User;
use app\common\basics\Logic;
use app\common\enum\FootprintEnum;
use app\common\model\distribution\DistributionGoods;
use app\common\model\goods\Goods;
use app\common\model\order\OrderGoods;
use app\common\model\goods\GoodsCollect;
use app\common\model\goods\GoodsClick;
use app\common\model\goods\GoodsSpec;
use app\common\model\goods\GoodsComment;
use app\common\model\goods\GoodsCommentImage;
use app\common\model\SearchRecord;
use app\common\enum\GoodsEnum;
use app\common\model\seckill\SeckillGoods;
use app\common\model\shop\Shop;
use app\common\model\team\TeamActivity;
use app\common\model\team\TeamFound;
use app\common\model\team\TeamGoods;
use app\common\model\user\UserLevel;
use app\common\model\Word;
use app\shopapi\logic\ShopLogic as ShopApiLogic;
use app\common\server\AreaServer;
use app\common\server\ConfigServer;
use app\common\server\UrlServer;
use app\common\server\WordUpdateServer;
use think\facade\Db;
use think\db\Raw;
use think\facade\Config;

use think\facade\Validate;

class GoodsLogic extends Logic
{

    /**
     * 商品详情
     */
    public static function getGoodsDetail($goodsId, $userId)
    {
        //获取用户折扣
        $discount = 10;
        if($userId){
            $user = User::where('id', $userId)->find();
            if($user && isset($user['level'])){
                $user_discount = UserLevel::where('id', $user['level'])->value('discount');
                if($user_discount && $user_discount > 0 && $user_discount <= 10){
                    $discount = $user_discount;
                }
            }
        }


        // 销售中商品：未删除/审核通过/已上架
        $onSaleWhere = [
            'del' => GoodsEnum::DEL_NORMAL, // 未删除
            'status' => GoodsEnum::STATUS_SHELVES, // 上架中
            'audit_status' => GoodsEnum::AUDIT_STATUS_OK, // 审核通过
        ];

        $goodsDetail = Goods::with(['goods_image', 'goods_item', 'shop'])
            ->field('id,hgou_lv,express_money,express_type,express_template_id,join_jc,type,name,image,video,remark,content,market_price,min_price,max_price,is_show_stock,stock,sales_actual,sales_virtual,clicks,clicks_virtual,shop_id,poster,delivery_type,is_list')
            ->where($onSaleWhere)
            ->where('id', $goodsId)
            ->findOrEmpty();

        if ($goodsDetail->isEmpty()) {
            self::$error = '商品已下架';
            return false;
        }

        //处理默认配送方式
        if ($goodsDetail['type'] == GoodsEnum::TYPE_VIRTUAL) {
            $goodsDetail['default_delivery_type'] = GoodsEnum::DELIVERY_VIRTUAL;
        } else {
            // 快递和自提
            $goodsDetail['default_delivery_type'] = (int)explode(',',$goodsDetail['delivery_type'])[0];
        }

        //判断是否是拼单集采商品,如果是拼单集采商品，获取拼单集采商品信息
        if($goodsDetail['join_jc'] == 1){
            $goodsDetail['min_price'] = GoodsItem::where('goods_id',$goodsId)->min('pdjc_price');
            $goodsDetail['max_price'] = GoodsItem::where('goods_id',$goodsId)->max('pdjc_price');
        }


        //如果`express_type` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '运费类型：1-包邮；2-统一运费；3-运费模板',
        //如果express_type =3 为运费模板 则显示默认全国的运费
        if($goodsDetail['express_type'] == GoodsEnum::EXPRESS_TYPE_TEMPLATE){
            $goodsDetail['express_money'] = Db::name('freight_config')->where('freight_id', $goodsDetail['express_template_id'])->where('region','all')->value('first_money');
            unset($goodsDetail['express_template_id']);
        }

        Db::startTrans();
        try{
            // 轮播图加域名
            foreach($goodsDetail['goods_image'] as &$item) {
                $item['uri'] = empty($item['uri']) ? '' : UrlServer::getFileUrl($item['uri']);
            }
            // 会员价
            $goodsDetail['member_price'] = 0;
            // 会员价数组
            $member_price = [];
            foreach ($goodsDetail['goods_item'] as &$goods_item) {
                $is_member = Goods::where('id',$goods_item['goods_id'])->value('is_member');
                $goods_item['is_member'] = $is_member;
                if($is_member == 1 && $discount && $userId){
                    $goods_item['member_price'] = round($goods_item['price']* $discount/10,2);
                    $goodsDetail['member_price'] =  round($goods_item['price']* $discount/10,2);
                    $member_price[] = $goodsDetail['member_price'];
                }
                // 规格图片处理
                $goods_item['image'] = empty($goods_item['image']) ? $goodsDetail['image'] : $goods_item['image'];

                if($goodsDetail['join_jc'] == 1){
                    $goods_item['price'] = $goods_item['pdjc_price'];

                }
            }

            !empty($member_price) && $goodsDetail['member_price'] = min($member_price);
            //计算商品回购率
            $hgou_lv=self::calculateRepurchaseRate($goodsId);
            //更新商品回购率
            // 增加点击量
            $goodsDetail->clicks += 1;
            $goodsDetail->hgou_lv = $hgou_lv;
            $goodsDetail->save();

            // 转数组
            $goodsDetailArr = $goodsDetail->toArray();

            $goodsDetailArr['poster'] = !empty($goodsDetailArr['poster']) ? UrlServer::getFileUrl($goodsDetailArr['poster']) : '';

            // 新增点击记录
            GoodsClick::create([
                'shop_id' => $goodsDetailArr['shop_id'],
                'user_id' => $userId,
                'goods_id' => $goodsId,
                'create_time' => time()
            ]);

            //店铺信息
            switch ($goodsDetailArr['shop']['type']){
                case 1 :
                    $type_desc = '官方自营';
                    break;
                case 2 :
                    $type_desc = '入驻商家';
                    break;
                default :
                    $type_desc = '入驻商家';
                    break;
            }
            $follow = Db::name('shop_follow')->where(['shop_id' => $goodsDetailArr['shop_id'],'status' => 1])->count('id');
            $goodsDetailArr['shop']['type_desc'] = $type_desc; //商家类型
            $goodsDetailArr['shop']['follow_num'] = $follow; //收藏人数

            //客服二维码
            $customer_image = ConfigServer::get('shop_customer_service','image','',$goodsDetailArr['shop_id']);
            if($customer_image){
                $customer_image = UrlServer::getFileUrl($customer_image);
            }
            $goodsDetailArr['shop']['customer_image'] = $customer_image;
            // 用户是否关注店铺
            $goodsDetailArr['shop']['shop_follow_status'] = 0;
            if($userId) { // 用户已登录
                $shopFollow = ShopFollow::where(['user_id'=>$userId, 'shop_id'=>$goodsDetailArr['shop_id']])->findOrEmpty();
                if(!$shopFollow->isEmpty()) {
                    $goodsDetailArr['shop']['shop_follow_status'] = $shopFollow['status'];
                }

                $shopfootprint = Db::name('goods_footprint')->where(['user_id'=>$userId, 'goods_id'=>$goodsId])->value('id');

                if(!empty($shopfootprint)) {
                    Db::name('goods_footprint')->where(['id'=>$shopfootprint])->inc('nums')->update(['update_time' => time()]);
                }else{
                    Db::name('goods_footprint')->insert(['user_id'=>$userId, 'goods_id'=>$goodsId, 'create_time'=>time()]);
                }
            }

            // 店铺在售商品数量
            $goodsDetailArr['shop']['goods_on_sale'] = Goods::where($onSaleWhere)
                ->where('shop_id', $goodsDetailArr['shop_id'])
                ->count();

            // 店铺推荐商品列表(9个)
            $goodsDetailArr['shop']['goods_list'] = Goods::field('id,name,image,market_price,min_price')
                ->where($onSaleWhere)
                ->where([
                    'shop_id' => $goodsDetailArr['shop_id'],
//                    'is_recommend' => 1, // 推荐
                ])
                ->order([
                    'sales_actual' => 'desc',
                    'id' => 'desc'
                ])
                ->limit(9)
                ->select()
                ->toArray();

            // 总销量 = 实际销量 + 虚拟销量
            $goodsDetailArr['sales_sum'] = $goodsDetailArr['sales_actual'] + $goodsDetailArr['sales_virtual'];

            // 优化销量显示逻辑
            $currentGoodsSales = $goodsDetailArr['sales_sum']; // 当前商品销量
            $displayText = '';

            if ($currentGoodsSales > 0) {
                // 当前商品有销量，显示商品销量
                $displayText ='拿样'.$currentGoodsSales.'件';
            } else {
                // 当前商品销量为0，获取全店销量
                $shopTotalSales = \think\facade\Db::name('goods')
                    ->where([
                        'shop_id' => $goodsDetailArr['shop_id'],
                        'del' => 0,
                        'status' => 1,
                        'audit_status' => 1
                    ])
                    ->sum('sales_actual');

                if ($shopTotalSales > 0) {
                    // 全店有销量，显示全店销量
                    $displayText = '全店拿样' . $shopTotalSales.'件';
                } else {
                    // 全店销量也为0，显示商品点击数
                    $totalClicks = $goodsDetailArr['clicks'] + $goodsDetailArr['clicks_virtual'];
                    if ($totalClicks > 0) {
                        $displayText = $totalClicks . '人想要';
                    } else {
                        // 点击数也为0，显示空
                        $displayText = '';
                    }
                }
            }

            $goodsDetailArr['sales_display_text'] = $displayText;
             if(!empty($user['nickname'])){

               AdminNotificationService::push('商品动态', $user['nickname'].'正在看商品'.$goodsDetail['name'], 'system', '', 0);
        }
            // 标识活动信息
            $goodsDetailArr['activity'] = [
                'type' => 0,
                'type_desc' => '普通商品'
            ];
            // 检查商品是否在参与活动，替换商品价格
            $goodsDetailArr = self::checkActivity($goodsDetailArr);
            // 是否收藏
            $goodsDetailArr['is_collect'] = 0;
            if($userId) { // 非游客
                $goodsCollect = GoodsCollect::where([
                    'user_id' => $userId,
                    'goods_id' => $goodsId
                ])->findOrEmpty();
                if(!$goodsCollect->isEmpty()) {
                    $goodsDetailArr['is_collect'] = $goodsCollect->status ? 1 : 0;
                }
            }
            // 规格项及规格值信息
            $goodsDetailArr['goods_spec'] = GoodsSpec::with('spec_value')
                ->where('goods_id', $goodsId)->select();


            //获取规格图片和库存信息
            foreach($goodsDetailArr['goods_spec'] as $key => &$value){
                foreach($value['spec_value'] as $subKey => &$subValue){
                    // 使用更安全的查询方式，避免直接使用字符串拼接造成SQL注入
                    $goodsItemInfo = Db::name('goods_item')
                        ->whereRaw("FIND_IN_SET(?, spec_value_ids)", [$subValue['id']])
                        ->field('image, stock')
                        ->find();

                    // 设置规格值图片
                    $subValue['image'] = empty($goodsItemInfo['image']) ? $goodsDetail['image'] : UrlServer::getFileUrl($goodsItemInfo['image']);

                    // 设置规格值库存信息
                    $subValue['stock'] = $goodsItemInfo['stock'] ?? 0;
                    $subValue['is_stock_out'] = ($subValue['stock'] <= 0) ? 1 : 0; // 1表示缺货，0表示有库存
                }
            }

            // 新增：按第一个规格值分组的库存信息
            $goodsDetailArr['spec_stock_groups'] = [];

            // 获取第一个规格（按ID排序最小的规格）
            $firstSpec = Db::name('goods_spec')
                ->where('goods_id', $goodsId)
                ->order('id', 'asc')
                ->find();

            if ($firstSpec) {
                // 获取第一个规格的所有规格值
                $firstSpecValues = Db::name('goods_spec_value')
                    ->where('spec_id', $firstSpec['id'])
                    ->where('goods_id', $goodsId)
                    ->order('id', 'asc')
                    ->select()
                    ->toArray();

                // 获取所有商品SKU项
                $allGoodsItems = Db::name('goods_item')
                    ->where('goods_id', $goodsId)
                    ->field('id,image,spec_value_ids,spec_value_str,price,stock,market_price,chengben_price,pdjc_price')
                    ->select()
                    ->toArray();

                // 按第一个规格值分组 - 显示所有规格值，不管是否有对应的SKU
                foreach ($firstSpecValues as $specValue) {
                    $groupData = [
                        'first_spec_name' => $firstSpec['name'],
                        'first_spec_value' => $specValue['value'],
                        'first_spec_value_id' => $specValue['id'],
                        'first_spec_value_image' => empty($specValue['image']) ? '' : UrlServer::getFileUrl($specValue['image']),
                        'combinations' => []
                    ];

                    // 查找包含该规格值的所有SKU组合
                    foreach ($allGoodsItems as $item) {
                        $specValueIds = explode(',', $item['spec_value_ids']);

                        // 检查是否包含当前规格值
                        if (in_array($specValue['id'], $specValueIds)) {
                            $combination = [
                                'item_id' => $item['id'],
                                'spec_value_ids' => $item['spec_value_ids'],
                                'spec_value_str' => $item['spec_value_str'],
                                'stock' => $item['stock'],
                                'price' => $item['price'],
                                'market_price' => $item['market_price'],
                                'image' => empty($item['image']) ? $goodsDetail['image'] : UrlServer::getFileUrl($item['image']),
                                'is_stock_out' => ($item['stock'] <= 0) ? 1 : 0
                            ];

                            // 如果是拼单集采商品，使用拼单集采价格
                            if ($goodsDetail['join_jc'] == 1) {
                                $combination['price'] = $item['pdjc_price'];
                                $combination['pdjc_price'] = $item['pdjc_price'];
                            }

                            // 添加成本价（如果存在）
                            if (!empty($item['chengben_price'])) {
                                $combination['chengben_price'] = $item['chengben_price'];
                            }

                            $groupData['combinations'][] = $combination;
                        }
                    }

                    // 显示所有规格值，即使没有对应的SKU组合
                    $goodsDetailArr['spec_stock_groups'][] = $groupData;
                }
            }

            // 商品评价
            // 首先检查当前商品是否有评价
            $goods_comment_count = Db::name('goods_comment')
                ->where(['goods_id' => $goodsId, 'del' => 0, 'status' => 1])
                ->count('id');

            $comment_source = 'goods'; // 评价来源标识：goods-商品评价，shop-店铺评价
            $query_params = ['goods_id' => $goodsId];

            // 如果当前商品没有评价，则查询店铺所有商品的评价
            if($goods_comment_count == 0) {
                $shop_comment_count = Db::name('goods_comment')
                    ->where(['shop_id' => $goodsDetailArr['shop_id'], 'del' => 0, 'status' => 1])
                    ->count('id');

                if($shop_comment_count > 0) {
                    $comment_source = 'shop';
                    $query_params = ['shop_id' => $goodsDetailArr['shop_id']];
                    $goods_comment_count = $shop_comment_count;
                }
            }

            // 获取评价分类统计
            $commentCategory = GoodsCommentLogic::category($query_params);
            $goodsDetailArr['comment']['percent'] = $commentCategory['percent'];
            $goodsDetailArr['comment']['source'] = $comment_source; // 标识评价来源

            // 计算平均评分
            if($goods_comment_count > 0){
                if($comment_source == 'goods') {
                    $all_comment = Db::name('goods_comment')->where(['goods_id' => $goodsId, 'del' => 0, 'status' => 1])->sum('goods_comment');
                } else {
                    $all_comment = Db::name('goods_comment')->where(['shop_id' => $goodsDetailArr['shop_id'], 'del' => 0, 'status' => 1])->sum('goods_comment');
                }
                $goods_comment = round($all_comment / $goods_comment_count, 2);
                $goodsDetailArr['comment']['goods_comment'] = $goods_comment;
            } else {
                $goodsDetailArr['comment']['goods_comment'] = 0;
            }

            // 最新一条评论
            $one = [];
            if($goods_comment_count > 0) {
                $oneQuery = GoodsComment::alias('gc')
                    ->field('gc.id,gc.goods_comment,gc.create_time,gc.comment,u.avatar,u.nickname,g.name as goods_name')
                    ->leftJoin('user u', 'u.id=gc.user_id')
                    ->leftJoin('goods g', 'g.id=gc.goods_id')
                    ->where([
                        ['gc.del', '=', 0],
                        ['gc.status', '=', 1],
                    ]);

                if($comment_source == 'goods') {
                    $oneQuery->where('gc.goods_id', '=', $goodsId);
                } else {
                    $oneQuery->where('gc.shop_id', '=', $goodsDetailArr['shop_id']);
                }

                $one = $oneQuery->order('create_time', 'desc')->findOrEmpty();

                if(!$one->isEmpty()) {
                    $one = $one->toArray();
                    // 头像
                    $one['avatar'] = UrlServer::getFileUrl($one['avatar']);
                    // 图片评价
                    $one['image'] = GoodsCommentImage::where('goods_comment_id', $one['id'])->column('uri');
                    foreach($one['image'] as $subKey => $subItem) {
                        $one['image'][$subKey] = UrlServer::getFileUrl($subItem);
                    }
                } else {
                    $one = [];
                }
            }
            $goodsDetailArr['comment']['one'] = $one;

            // 判断是否是拼团商品
            $teamActivity = (new TeamActivity())
                ->field(['id,people_num,team_max_price,team_min_price,sales_volume,activity_end_time,share_title,share_intro'])
                ->where([
                    ['goods_id', '=', $goodsId],
                    ['audit', '=', 1],
                    ['status', '=', 1],
                    ['del', '=', 0],
                    ['activity_start_time', '<=', time()],
                    ['activity_end_time', '>=', time()]
            ])->findOrEmpty()->toArray();

            if ($teamActivity) {
                $teamFound = (new TeamFound())->alias('TF')
                    ->field(['TF.*', 'U.nickname,U.avatar'])
                    ->limit(8)
                    ->order('id desc')
                    ->where('TF.team_activity_id', '=', $teamActivity['id'])
                    ->where('TF.people','exp',' > TF.join ')
                    ->where([
                        ['status', '=', 0],
                        ['invalid_time', '>=', time()]
                    ])->join('user U', 'U.id=TF.user_id')
                      ->select()->toArray();

                foreach ($teamFound as &$found) {
                    unset($found['shop_id']);
                    unset($found['team_sn']);
                    unset($found['goods_snap']);
                    unset($found['team_end_time']);
                    $found['avatar'] = UrlServer::getFileUrl($found['avatar']);
                    $found['surplus_time'] = intval($found['invalid_time'] - time());
                }

                $teamActivity['share_title'] = !empty($teamActivity['share_title']) ? $teamActivity['share_title'] : $goodsDetailArr['name'];
                $teamActivity['share_intro'] = !empty($teamActivity['share_intro']) ? $teamActivity['share_intro'] : $goodsDetailArr['remark'];

                $goodsDetailArr['activity'] = ['type'=>2, 'type_desc'=>'拼团商品', 'info'=>$teamActivity, 'found'=>$teamFound];
                $teamGoods = (new TeamGoods())->where(['team_id'=>$teamActivity['id']])->select()->toArray();
                foreach ($goodsDetailArr['goods_item'] as &$item) {
                    foreach ($teamGoods as $team) {
                        if ($item['id'] === $team['item_id']) {
                            $item['team_price'] = $team['team_price'];
                        }
                    }
                }
            }

            // 预估佣金(计算出最高可得佣金)
            $goodsDetailArr['distribution'] = self::getDistribution($goodsId, $userId);

            // 虚拟浏览量
            $goodsDetailArr['clicks'] += $goodsDetailArr['clicks_virtual'];
            //将content中字符串中是style样式都去除掉
            $goodsDetailArr['content'] = preg_replace('/style=".*?"/', '', $goodsDetailArr['content']);
            // 记录访问足迹
            event('Footprint', [
                'type'    => FootprintEnum::BROWSE_GOODS,
                'user_id' => $userId,
                'foreign_id' => $goodsId
            ]);
            $shop_address=Db::name('shop')->field('province_id,city_id,district_id,address,total_sales')->where('id', $goodsDetailArr['shop_id'])->find();
            $shop_province = $shop_address['province_id'] ?? ''; //省份
              $goodsDetailArr['total_sales'] =$shop_address['total_sales'];
            $shop_city = $shop_address['city_id'] ?? ''; //城市
            $shop_district = $shop_address['district_id'] ?? ''; //县区
            $shop_address = $shop_address['address'] ?? ''; //县区
            $shop_address['address'] ?? ''; //详细地址

            $goodsDetailArr['shop_province'] = AreaServer::getAddress($shop_province);
            $goodsDetailArr['shop_city'] = AreaServer::getAddress($shop_city);
            $goodsDetailArr['shop_district'] = AreaServer::getAddress($shop_district);
            $goodsDetailArr['shop_address'] =$shop_address;//1
            Db::commit();
            return $goodsDetailArr;
        }catch(\Exception $e) {
            Db::rollback();
            self::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * 热销榜单
     */
    public static  function getHotList($get)
    {
        // 销售中商品：未删除/审核通过/已上架
        $where = [
            ['del', '=', GoodsEnum::DEL_NORMAL],  // 未删除
            ['status', '=', GoodsEnum::STATUS_SHELVES], // 上架中
            ['audit_status', '=', GoodsEnum::AUDIT_STATUS_OK], // 审核通过
            ['join_jc', '=', 0], // 没有加入拼单集采通过
        ];
        $order = [
            'sales_total' => 'desc', // 实际销量+虚拟销量倒序
            'sales_actual' => 'desc', // 实际销量倒序
            'id' => 'desc'
        ];

        return self::getGoodsListTemplate($where, $order, $get);
    }

    /**
     * 商品列表
     */
    public static function getGoodsList($get)
    {
        // 销售中商品：未删除/审核通过/已上架
        $where = [
            ['del', '=', GoodsEnum::DEL_NORMAL],  // 未删除
            ['status', '=', GoodsEnum::STATUS_SHELVES], // 上架中
            ['audit_status', '=', GoodsEnum::AUDIT_STATUS_OK], // 审核通过
            ['join_jc', '=', 0], // 没有加入拼单集采通过
        ];

        $order=[];
        // 获取有效店铺ID列表，传递给推荐算法
        $validShopIds = self::getValidShopIds();
        $get['valid_shop_ids'] = $validShopIds;

        //如果用户没登录
        if($get['user_id'] && !empty($get['keyword'])) { // 记录关键词
            $get['filed_asc']=self::getRecommendGoods($get);
        }else if($get['user_id'] && empty($get['keyword'])){
            $get['filed_asc']=self::getRecommendGoods($get);

        }else{
           if(!empty($get['sort_by_price']) || !empty($get['sort_by_sales']) || !empty($get['sort_by_create'])){

            }else{
                $order = [
                    'sort_weight' => 'asc', // 商品权重，数字越小权重越大
                    'sort' => 'asc',
                    'id' => 'desc'
                ];
                $get['is_rand']=1;
                }

        }

        #获取当前商品ID的属性
        return self::getGoodsListTemplate($where, $order, $get);
    }


    /**
     * 获取有效店铺ID列表（统一的店铺筛选逻辑）
     * @return array 有效店铺ID列表
     */
    public static function getValidShopIds()
    {
        static $validShopIds = null;

        // 使用静态缓存，避免重复计算
        if ($validShopIds !== null) {
            return $validShopIds;
        }

        $shopApiLogic = new ShopApiLogic();
        $allShops = Shop::where('del', 0)->select()->toArray();

        // 过滤资料完整的店铺
        $validShops = array_filter($allShops, function($shop) use ($shopApiLogic) {
            $completionResult = $shopApiLogic->checkProfileCompletion($shop['id']);
            return empty($completionResult);
        });
        $validShopIds = array_column($validShops, 'id');

        // 排除需要过滤的店铺ID
        $filteredShopIds = self::filterShopsIds();
        if (!empty($filteredShopIds)) {
            $validShopIds = array_diff($validShopIds, $filteredShopIds);
        }

        return $validShopIds;
    }

    /**
     * 商品列表模板
     * 作用：代码复用
     */
    public static function getGoodsListTemplate($where, $order, $get)
    {
        // 添加店铺筛选条件
        $validShopIds = self::getValidShopIds();
        if (!empty($validShopIds)) {
            $where[] = ['shop_id', 'in', $validShopIds];
        }
        $get['where_raw'] = isset($get['where_raw']) ? $get['where_raw'] : '';
        //增加一个随机数
        $whereOr = isset($get['whereOr']) ? $get['whereOr'] : [];
        $goods_tui_id=$get['goods_tui_id'] ?? '';
        if ( $goods_tui_id != '' ){
            #获取当前商品ID的属性
            $second_cate_id=Db::name('goods')->where('id', $goods_tui_id)->value('second_cate_id');
            if(!empty($second_cate_id)){
                $where[] = ['second_cate_id', '=',$second_cate_id];
            }

            unset($get['shop_id']);
            unset($get['is_recommend']);
        }
        // 平台分类
        if(isset($get['platform_cate_id']) && !empty($get['platform_cate_id']) && filter_var($get['platform_cate_id'], FILTER_VALIDATE_INT)) {
            $where[] = ['first_cate_id|second_cate_id|third_cate_id', '=', $get['platform_cate_id']];
        }

        //二级类目
        if(isset($get['second_cate_id']) && !empty($get['second_cate_id'])) {
            $tid=1;
            $where[] = ['second_cate_id', '=', $get['second_cate_id']];
        }
        // 品牌
        if(isset($get['brand_id']) && !empty($get['brand_id']) && filter_var($get['brand_id'], FILTER_VALIDATE_INT)) {
            $where[] = ['brand_id', '=', $get['brand_id']];
        }

        // 关键词处理
        if (isset($get['keyword']) && !empty($get['keyword'])) {  
            // 清理和预处理关键词
            $keyword = trim(urldecode($get['keyword']));

            // 安全过滤：移除HTML标签和特殊字符
            if (preg_match('/<[^>]+>/', $keyword)) {
                $keyword = strip_tags($keyword);
            }

            // 记录原始关键词，用于后续高亮处理
            $get['original_keyword'] = $keyword;

            // 检查是否包含空格（多个关键词）
            $keywordParts = preg_split('/\s+/', $keyword);
            $isMultiKeyword = count($keywordParts) > 1;

            // 使用简单分词处理关键词，避免依赖阿里云服务
            $segmentedWords = [$keyword];

            // 尝试使用阿里云分词（如果可用）
            try {
                $words = Alisegment($keyword);
                if (!empty($words['words']) && is_array($words['words'])) {
                    $segmentedWords = $words['words'];
                }
            } catch (\Exception $e) {
                // 如果分词失败，记录错误但继续使用原始关键词
              
                \think\facade\Log::error('阿里云分词失败，使用原始关键词: ' . $e->getMessage());
            }

            // 合并空格分割的关键词和分词结果
            $allKeywordParts = array_unique(array_merge($keywordParts, $segmentedWords));

            // 记录关键词部分，用于后续高亮处理
            $get['keyword_parts'] = $keywordParts;
            $get['segmented_words'] = $segmentedWords;
            $get['all_keyword_parts'] = $allKeywordParts;

            // 关键词长度统计（中文字符安全计数）
            $wordCount = mb_strlen($keyword, 'UTF-8');

            // 记录用户搜索关键词（条件：有用户ID且关键词长度>1）
            if (!empty($get['user_id']) && $wordCount > 1) {
                self::recordKeyword($keyword, $get['user_id']);
            }

            // 优先使用 MeiliSearch 搜索，失败再回退到数据库
          

            // 先尝试使用 MeiliSearch
            $useMeiliSearch = true; // 启用 MeiliSearch
            if ($useMeiliSearch) {
                try {
                    // 初始化MeiliSearch客户端
                    $meili = new \app\common\library\MeiliSearch();

                    // 构建搜索过滤条件 - 使用优化的转换方法
                    $filter = self::buildMeiliSearchFilter($where);

                    // 构建搜索选项
                    $searchOptions = [
                        'limit' => (int)$get['page_size'],
                        'offset' => ((int)$get['page_no'] - 1) * (int)$get['page_size'],
                        'attributesToRetrieve' => ['id', 'name', 'image', 'remark', 'min_price', 'market_price',
                            'sales_actual', 'sales_virtual', 'first_cate_id', 'second_cate_id', 'third_cate_id',
                            'sort_weight', 'brand_id', 'shop_id', 'is_hot', 'goods_label', 'goods_label_top', 'join_jc', 'year_jc_sales', 'year_sales', 'video',
                            'category_path', 'brand_name', 'tags', 'content'], // Added new fields, removed split_word
                        'attributesToHighlight' => ['name', 'remark', 'content', 'category_path', 'brand_name', 'tags'], // Updated
                        'highlightPreTag' => '<b style="color: red;">',
                        'highlightPostTag' => '</b>',
                        'matchingStrategy' => 'all',
                        'attributesToSearchOn' => ['name', 'remark', 'content', 'category_path', 'brand_name', 'tags'], // Updated
                        // 'minWordSizeForTypos' => 1, // 允许更短词的拼写错误
                        'cropLength' => 30, // 限制高亮片段长度
                    ];

                    // 添加过滤条件
                    if (!empty($filter)) {
                        $searchOptions['filter'] = implode(' AND ', $filter);
                    }

                    // 添加排序条件 - 使用优化的排序映射
                    $sortOptions = self::buildMeiliSearchSort($get, $order);
                    if (!empty($sortOptions)) {
                        $searchOptions['sort'] = $sortOptions;
                    }

                    // 执行搜索

                    $searchResults = []; // Initialize searchResults
                    $isOriginalSingleTerm = (strpos($keyword, ' ') === false); // Check if keyword (after initial processing) has spaces

                    if ($isOriginalSingleTerm) {
                        // Original input was a single logical unit like "老人鞋"
                        $searchQueryAttempt = '"' . $keyword . '"'; // Enforce phrase search for MeiliSearch
                       
                        $searchResults = $meili->advancedSearch('goods', $searchQueryAttempt, $searchOptions);

                        if (empty($searchResults['hits'])) {
                            // Phrase search failed, try non-phrase search with the original single term
                            $searchResults = $meili->advancedSearch('goods', $keyword, $searchOptions);
                        }
                    } else {
                        // Original input had spaces, e.g., "老人 透气 鞋".
                        // MeiliSearch with `matchingStrategy: 'all'` on this multi-term query.
                        $searchResults = $meili->advancedSearch('goods', $keyword, $searchOptions);
                    }

                    // 如果上述各种基于原始关键词的尝试后仍然没有结果，并且有分词结果，则尝试基于分词的搜索
                    if (empty($searchResults['hits']) && !empty($segmentedWords)) {
                        // 使用分词结果中的第一个词进行搜索
                        $primaryWord = $segmentedWords[0];
                        $searchResults = $meili->advancedSearch('goods', $primaryWord, $searchOptions);
                      
                        // 如果还是没有结果，并且分词结果多于一个，尝试使用所有分词结果组合搜索
                        if (empty($searchResults['hits']) && count($segmentedWords) > 1) {
                            $combinedSegmentedKeyword = implode(' ', $segmentedWords);
                            $searchResults = $meili->advancedSearch('goods', $combinedSegmentedKeyword, $searchOptions);
                        }
                    }
                   
                    // 如果搜索成功，使用搜索结果
                    if (isset($searchResults['hits']) && !empty($searchResults['hits'])) {
                        \think\facade\Log::info('MeiliSearch搜索结果数量: ' . count($searchResults['hits']));

                        // 提取商品ID列表
                        $goodsIds = array_column($searchResults['hits'], 'id');

                        // 保存高亮信息
                        $highlightInfo = [];
                        foreach ($searchResults['hits'] as $hit) {
                            if (isset($hit['_formatted'])) {
                                $highlightInfo[$hit['id']] = [
                                    'name' => $hit['_formatted']['name'] ?? $hit['name'],
                                    'remark' => $hit['_formatted']['remark'] ?? $hit['remark'],
                                    'content' => $hit['_formatted']['content'] ?? ($hit['content'] ?? ''),
                                    'category_path' => $hit['_formatted']['category_path'] ?? ($hit['category_path'] ?? []),
                                    'brand_name' => $hit['_formatted']['brand_name'] ?? ($hit['brand_name'] ?? ''),
                                    'tags' => $hit['_formatted']['tags'] ?? ($hit['tags'] ?? [])
                                ];
                            }
                        }

                        // 将高亮信息保存到$get中，以便后续处理
                        $get['highlight_info'] = $highlightInfo;

                        // 使用搜索结果的ID列表
                        if (!empty($goodsIds)) {
                            $where[] = ['id', 'in', $goodsIds];
                            // 保持结果顺序
                            $get['meili_search_ids'] = $goodsIds;
                        }
                    } else {
                       
                        // 如果没有结果，回退到传统搜索, ensuring split_word is not used if deprecated
                        self::fallbackToTraditionalSearch($keyword, $keywordParts, $isMultiKeyword, $get, $where, true); // Pass true for useNewSearchFields
                    }
                } catch (\Exception $e) {
                    // 如果MeiliSearch出错，记录错误并回退到传统搜索
                    \think\facade\Log::error('MeiliSearch异常: ' . $e->getMessage());

                    // 使用传统搜索方法, ensuring split_word is not used if deprecated
                    self::fallbackToTraditionalSearch($keyword, $keywordParts, $isMultiKeyword, $get, $where, true); // Pass true for useNewSearchFields
                }
            }
        }

        // 店铺id
        if(isset($get['shop_id']) && !empty($get['shop_id']) && filter_var($get['shop_id'], FILTER_VALIDATE_INT)) {
            $where[] = ['shop_id', '=', $get['shop_id']];
        }

        // 店铺推荐
        if (Validate::must($get['is_recommend'] ?? '')) {
            $where[] = [ 'is_recommend', '=', $get['is_recommend'] ];
        }

        // 店铺分类
        if(isset($get['shop_cate_id']) && !empty($get['shop_cate_id']) && filter_var($get['shop_cate_id'], FILTER_VALIDATE_INT)) {
            $where[] = ['shop_cate_id', '=', $get['shop_cate_id']];
        }

        // 销量排序(实际销量 + 虚拟销量)
        if(isset($get['sort_by_sales']) && !empty($get['sort_by_sales'])) {
            $elt = ['sales_total'=> trim($get['sort_by_sales'])];
            $order = array_merge($elt, $order);
        }

        // 价格排序
        if(isset($get['sort_by_price']) && !empty($get['sort_by_price'])) {
            $elt = ['min_price'=> trim($get['sort_by_price'])];
            $order = array_merge($elt, $order);
        }

        // 新品排序
        if(isset($get['sort_by_create']) && !empty($get['sort_by_create'])) {
            $elt = ['create_time'=> trim($get['sort_by_create'])];
            $order = array_merge($elt, $order);
        }

        //随机排序
        if(isset($get['is_rand']) && !empty($get['is_rand'])) {
            $get['filed_asc'] ='RAND()';
        }
        $field = 'id,goods_label_top,goods_label,is_hot,year_jc_sales,join_jc,image,remark,year_sales,video,name,min_price,market_price,sales_actual,first_cate_id,
        second_cate_id,third_cate_id,sort_weight,brand_id,shop_id,sales_virtual,
        (sales_actual + sales_virtual) as sales_total';
        
        // 检查是否有MeiliSearch搜索结果
        if (isset($get['meili_search_ids']) && !empty($get['meili_search_ids'])) {
            // 使用MeiliSearch的结果顺序
            $goodsIds = $get['meili_search_ids'];

            // 使用FIELD函数保持MeiliSearch返回的顺序
            $orderByIds = 'FIELD(id,' . implode(',', $goodsIds) . ')';

            if(isset($tid)){
                $list = Goods::with(['shop','goods_image'])
                    ->field($field)
                    ->where($where)
                    ->where($get['where_raw'])
                    ->where(function ($query) use ($whereOr) {
                        $query->whereOr($whereOr);
                    })
                    ->orderRaw($orderByIds)
                    ->select();
            } else {
               
                $list = Goods::with(['shop'])
                    ->field($field)
                    ->where($where)
                    ->where($get['where_raw'])
                    ->where(function ($query) use ($whereOr) {
                        $query->whereOr($whereOr);
                    })
                    ->orderRaw($orderByIds)
                    ->select();
            }
        } else {
            // 使用传统查询方式
            if(isset($tid)){
                $list = Goods::with(['shop','goods_image'])
                    ->field($field)
                    ->where($where)
                    ->where($get['where_raw'])
                    ->where(function ($query) use ($whereOr) {
                        $query->whereOr($whereOr);
                    })
                    ->order($order)
                    ->page($get['page_no'], $get['page_size'])
                    ->select();
            } else {
                // 构建基础查询
                $query = Goods::with(['shop'])
                    ->field($field)
                    ->where($where)
                    ->where($get['where_raw'])
                    ->where(function ($query) use ($whereOr) {
                        $query->whereOr($whereOr);
                    });

                // 处理排序逻辑
                if (isset($get['filed_asc']) && !empty($get['filed_asc'])) {
                    $sortField = trim($get['filed_asc']);

                    // 检查是否为FIELD排序
                    if (strpos($sortField, 'FIELD(') === 0) {
                        // 提取FIELD函数中的商品ID
                        if (preg_match('/FIELD\(id,\s*([0-9,\s]+)\)/', $sortField, $matches)) {
                            $fieldIds = array_map('intval', array_filter(explode(',', $matches[1])));

                            if (!empty($fieldIds)) {
                                // 构建CASE WHEN排序：推荐商品在前，其他商品在后
                                $fieldIdStr = implode(',', $fieldIds);
                                $caseOrderSql = "CASE WHEN id NOT IN ({$fieldIdStr}) THEN 0 ELSE FIELD(id, {$fieldIdStr}) END";

                                // 构建完整的排序：先按推荐顺序，再按默认排序
                                $defaultOrderSql = [];
                                foreach ($order as $field => $direction) {
                                    $defaultOrderSql[] = "{$field} {$direction}";
                                }
                                $defaultOrderStr = !empty($defaultOrderSql) ? ', ' . implode(', ', $defaultOrderSql) : ', id DESC';

                                $finalOrderSql = $caseOrderSql . $defaultOrderStr;
                                $query->orderRaw($finalOrderSql);
                            } else {
                                // FIELD函数格式错误，使用默认排序
                                $query->order($order);
                            }
                        } else {
                            // FIELD函数格式错误，使用默认排序
                            $query->order($order);
                        }
                    } else {
                        // 非FIELD排序（如RAND()等），直接使用
                        try {
                            $query->orderRaw($sortField);
                        } catch (\Exception $e) {
                            // 排序字段有问题时，降级到默认排序
                            \think\facade\Log::warning('排序字段异常，降级到默认排序: ' . $e->getMessage());
                            $query->order($order);
                        }
                    }
                } else {
                    // 使用默认排序
                    $query->order($order);
                }

                $list = $query->page($get['page_no'], $get['page_size'])->select();
            }
        }


        foreach ($list as &$item) {
            $item['shop_type'] = $item['shop']['type']??'';
            if(isset($item['goods_image'])){
                foreach ($item['goods_image'] as &$val) {
                    $val['uri'] =UrlServer::getFileUrl($val['uri']);
                }
                $item['collect_nums']=Db::name('goods_collect')->where('goods_id', $item['id'])->count()?:10;
            }else{
                unset($item['shop']);
            }
            //如果是拼单集采商品显示拼单集采价格
            if($item['join_jc']==1){
                $item['min_price']=Db::name('goods_item')->where('goods_id',$item['id'])->min('pdjc_price');
            }

            // 处理搜索关键词高亮
            if (isset($get['keyword']) && !empty($get['keyword'])) {
                // 保存原始名称和描述
                $item['name_original'] = $item['name'];
                $item['remark_original'] = $item['remark'];

                // 检查是否有MeiliSearch的高亮结果
                if (isset($get['highlight_info']) && !empty($get['highlight_info']) && isset($get['highlight_info'][$item['id']])) {
                    // 使用MeiliSearch的高亮结果
                    $highlightInfo = $get['highlight_info'][$item['id']];
                    $item['name_highlight'] = $highlightInfo['name'] ?? $item['name'];
                    $item['remark_highlight'] = $highlightInfo['remark'] ?? $item['remark'];
                } else {
                    // 使用传统高亮处理方式
                    // 检查是否是多关键词搜索
                    $isMultiKeyword = isset($get['keyword_parts']) && count($get['keyword_parts']) > 1;

                    if ($isMultiKeyword) {
                        // 多关键词高亮处理
                        $keywordParts = $get['keyword_parts'] ?? [];
                        $highlightedName = $item['name'];
                        $highlightedRemark = $item['remark'];

                        // 对每个关键词部分进行高亮处理
                        foreach ($keywordParts as $part) {
                            if (empty(trim($part))) continue;

                            $pattern = '/(' . preg_quote(trim($part), '/') . ')/iu';
                            $replacement = '<b style="color: red;">$1</b>';
                            $highlightedName = preg_replace($pattern, $replacement, $highlightedName);
                            $highlightedRemark = preg_replace($pattern, $replacement, $highlightedRemark);
                        }

                        // 高亮处理分词结果
                        $segmentedWords = $get['segmented_words'] ?? [];
                        if (!empty($segmentedWords) && is_array($segmentedWords)) {
                            foreach ($segmentedWords as $word) {
                                // 只处理长度>=2且不是原始关键词部分的分词结果
                                if (mb_strlen(trim($word), 'UTF-8') >= 2 && !in_array(trim($word), $keywordParts)) {
                                    $pattern = '/(' . preg_quote(trim($word), '/') . ')/iu';
                                    $replacement = '<b style="color: red;">$1</b>';
                                    $highlightedName = preg_replace($pattern, $replacement, $highlightedName);
                                    $highlightedRemark = preg_replace($pattern, $replacement, $highlightedRemark);
                                }
                            }
                        }

                        // 添加高亮字段
                        $item['name_highlight'] = $highlightedName;
                        $item['remark_highlight'] = $highlightedRemark;
                    } else {
                        // 单关键词高亮处理
                        $originalKeyword = $get['original_keyword'] ?? '';
                        $highlightedName = $item['name'];
                        $highlightedRemark = $item['remark'];

                        if (!empty($originalKeyword)) {
                            $pattern = '/(' . preg_quote($originalKeyword, '/') . ')/iu';
                            $replacement = '<b style="color: red;">$1</b>';
                            $highlightedName = preg_replace($pattern, $replacement, $highlightedName);
                            $highlightedRemark = preg_replace($pattern, $replacement, $highlightedRemark);
                        }

                        // 高亮处理分词结果
                        $segmentedWords = $get['segmented_words'] ?? [];
                        if (!empty($segmentedWords) && is_array($segmentedWords)) {
                            foreach ($segmentedWords as $word) {
                                if (trim($word) !== $originalKeyword && mb_strlen(trim($word), 'UTF-8') >= 2) {
                                    $pattern = '/(' . preg_quote(trim($word), '/') . ')/iu';
                                    $replacement = '<b style="color: red;">$1</b>';
                                    $highlightedName = preg_replace($pattern, $replacement, $highlightedName);
                                    $highlightedRemark = preg_replace($pattern, $replacement, $highlightedRemark);
                                }
                            }
                        }

                        // 添加高亮字段
                        $item['name_highlight'] = $highlightedName;
                        $item['remark_highlight'] = $highlightedRemark;
                    }
                }

                // 恢复原始字段
                $item['name'] = $item['name_original'];
                $item['remark'] = $item['remark_original'];

                // 删除临时字段
                unset($item['name_original']);
                unset($item['remark_original']);
            }

            //缓存读取商品标签，有效期6个小时
            $goodsLabelCacheKey = 'goods_label_' . $item['id'];
            $goodsLabelCache = cache($goodsLabelCacheKey);
            if (!$goodsLabelCache) {
                $goodsLabel = Db::name('goods_column')
                    ->field('name,image')
                    ->whereRaw("FIND_IN_SET(id,?)", [$item['goods_label']])
                    ->select()->toArray();
                cache($goodsLabelCacheKey, $goodsLabel, 3600 * 1);
                $item['goods_label'] = $goodsLabel;
            } else {
                $item['goods_label'] = $goodsLabelCache;
            }

            $goodsLabelTopCacheKey = 'goods_label_top_' . $item['id'];
            $goodsLabelTopCache = cache($goodsLabelTopCacheKey);
            if (!$goodsLabelTopCache) {
                $goodsLabelTop = Db::name('goods_column')
                    ->field('name,image')
                    ->whereRaw("FIND_IN_SET(id,?)", [$item['goods_label_top']])
                    ->select()->toArray();
                cache($goodsLabelTopCacheKey, $goodsLabelTop, 3600 * 6);
                $item['goods_label_top'] = $goodsLabelTop;
            } else {
                $item['goods_label_top'] = $goodsLabelTopCache;
            }

            $isHotCacheKey = 'is_hot_' . $item['id'];
            $isHotCache = cache($isHotCacheKey);
            if (!$isHotCache) {
                $isHot = Db::name('goods_column')
                    ->field('name,image')
                    ->whereRaw("FIND_IN_SET(id,?)", [$item['is_hot']])
                    ->select()->toArray();
                cache($isHotCacheKey, $isHot, 3600 * 6);
                $item['is_hot'] = $isHot;
            } else {
                $item['is_hot'] = $isHotCache;
            }
        }




        // 假设 $list 是一个对象数组，转换为数组后再处理
        if(isset($get['is_limit']) && !empty($get['is_limit'])){
            // 将对象数组转换为普通数组
            $listArray = json_decode(json_encode($list), true);
            // 取出前3个元素作为top数组
            $top = array_slice($listArray, 0, 3);
            // 剩下的元素作为list数组
            $listArray = array_slice($listArray, 3);
            // 组装最终数据结构
            $finalList = ['top' => $top, 'list' => $listArray];
        }
        else{
            $finalList = $list ? $list->toArray() : [];
        }

        $count = Goods::where($where)->count();

        $more = is_more($count, $get['page_no'], $get['page_size']);

        $data = [
            'lists'         => $finalList,
            'page_no'       => $get['page_no'],
            'page_size'     => $get['page_size'],
            'count'         => $count,
            'more'          => $more
        ];
        return $data;
    }

    /**
     * 根据商品栏目获取商品列表
     *
     * @param int $columnId 栏目ID
     * @param int $page_no 页码
     * @param int $page_size 每页数量
     * @return array 商品列表
     */
    public static function getGoodsListByColumnId($columnId, $page_no, $page_size)
    {
        $page_size=$page_size??10;
        // 销售中商品：未删除/审核通过/已上架
        $onSaleWhere = [
            ['del', '=', GoodsEnum::DEL_NORMAL],
            ['status', '=', GoodsEnum::STATUS_SHELVES],
            ['audit_status', '=', GoodsEnum::AUDIT_STATUS_OK],
        ];

        // 使用统一的店铺筛选逻辑
        $validShopIds = self::getValidShopIds();
        if (!empty($validShopIds)) {
            $onSaleWhere[] = ['shop_id', 'in', $validShopIds];
        }

        // 添加栏目ID过滤条件
        if (!empty($columnId)) {
            $onSaleWhere[] = ['column_ids', 'like', "%{$columnId}%"];
        }

        $order = [
            'sort_weight' => 'asc', // 数字越小，权重越大
            'sales_actual' => 'desc',
            'id' => 'desc'
        ];

        $list = Goods::field('id,name,image,market_price,min_price,sales_actual,column_ids,sort_weight,sales_virtual,(sales_actual + sales_virtual) as sales_total')
            ->where($onSaleWhere)
            ->order($order)
            ->page($page_no, $page_size)
            ->select();

        $count = Goods::where($onSaleWhere)
            ->count();

        $list = $list ? $list->toArray() : [];

        $more = is_more($count, $page_no, $page_size);
        //获取商品标签

        $data = [
            'lists'          => $list,
            'page_no'       => $page_no,
            'page_size'     => $page_size,
            'count'         => $count,
            'more'          => $more
        ];
        return $data;
    }

    /**
     * @notes 获取已删除、已冻结、已暂停营业、已到期店铺的id
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2021/7/20 14:29
     */
    public static function filterShopsIds()
    {
        // 已删除、已冻结、已暂停营业的店铺
        $invalidShops = Shop::field('id,name')->whereOr([
            ['del', '=', 1], // 已删除
            ['is_freeze', '=', 1], // 已冻结
            ['is_run', '=', 0] // 暂停营业
        ])->select()->toArray();

        // 已过期的店铺
        $expiredShops = Shop::field('id,name')->where([
            ['expire_time', '<>', 0],
            ['expire_time', '<=', time()],
        ])->select()->toArray();

        $filterShops = array_merge($invalidShops, $expiredShops);
        $filterShopsIds = array_column($filterShops, 'id');
        return $filterShopsIds;
    }

    /**
     * 记录关键词
     */
    public static function recordKeyword($keyword, $user_id)
    {
        $record = SearchRecord::where(['user_id'=>$user_id,'keyword'=>$keyword,'del'=>0])->find();
        if($record){
            // 有该关键词记录, 更新
            return SearchRecord::where(['id'=>$record['id']])->update(['count'=>Db::raw('count+1'),'update_time'=>time()]);
        }
        // 无该关键词记录 > 新增
        return SearchRecord::create([
            'user_id'=>$user_id,
            'keyword'=>$keyword,
            'count' => 1,
            'update_time' => time(),
            'del' => 0
        ]);
    }

    /**
     * 回退到传统搜索方法
     *
     * @param string $keyword 搜索关键词
     * @param array $keywordParts 关键词分词结果
     * @param bool $isMultiKeyword 是否是多关键词
     * @param array &$get 请求参数
     * @param array &$where 查询条件
     */
    protected static function fallbackToTraditionalSearch($keyword, $keywordParts, $isMultiKeyword, &$get, &$where, $useNewSearchFields = false)
    {
        // 使用简单分词处理（如果之前没有处理过）
        if (!isset($get['segmented_words']) || empty($get['segmented_words'])) {
            $segmentedWords = [$keyword];

            // 尝试使用阿里云分词（如果可用）
            try {
                $words = Alisegment($keyword);
                if (!empty($words['words']) && is_array($words['words'])) {
                    $segmentedWords = $words['words'];
                }
            } catch (\Exception $e) {
                // 如果分词失败，记录错误但继续使用原始关键词
                \think\facade\Log::error('传统搜索分词失败，使用原始关键词: ' . $e->getMessage());
            }
            $get['segmented_words'] = $segmentedWords;
        } else {
            $segmentedWords = $get['segmented_words'];
        }

        // 合并空格分割的关键词和分词结果
        $allKeywordParts = isset($get['all_keyword_parts']) ? $get['all_keyword_parts'] : array_unique(array_merge($keywordParts, $segmentedWords));
        $get['all_keyword_parts'] = $allKeywordParts;

        // 构建搜索条件
        if ($useNewSearchFields) {
            // Based on the new MeiliSearch settings, 'split_word' is removed.
            // 'category_path', 'brand_name', 'tags' are array/object types in MeiliSearch and cannot be directly used in SQL LIKE.
            // We will search in 'name', 'remark', 'content'.
            // For 'category_path', 'brand_name', 'tags', a more complex subquery or join would be needed if traditional SQL must replicate this.
            // For simplicity in fallback, we'll stick to text fields that are directly comparable.
            $searchFields = ['name', 'remark', 'content'];
        } else {
            $searchFields = ['name', 'remark', 'split_word'];
        }

        // 优化搜索逻辑：提高精确性和相关性
        $where[] = function($query) use ($allKeywordParts, $keyword, $searchFields) {
            // 1. 最高优先级：完整关键词精确匹配
            $query->whereOr(function($subQuery) use ($keyword, $searchFields) {
                foreach ($searchFields as $field) {
                    $subQuery->whereOr($field, 'like', '%'.$keyword.'%');
                }
            });

            // 2. 处理同义词和相关词匹配
            $synonyms = self::getSynonyms($keyword);
            if (!empty($synonyms)) {
                foreach ($synonyms as $synonym) {
                    $query->whereOr(function($subQuery) use ($synonym, $searchFields) {
                        foreach ($searchFields as $field) {
                            $subQuery->whereOr($field, 'like', '%'.$synonym.'%');
                        }
                    });
                }
            }

            // 3. 分词匹配（仅当原关键词是复合词时）
            if (count($allKeywordParts) > 1) {
                // 对于复合词，要求所有分词都匹配（AND逻辑）
                $query->whereOr(function($subQuery) use ($allKeywordParts, $keyword, $searchFields) {
                    $validParts = array_filter($allKeywordParts, function($part) use ($keyword) {
                        return !empty(trim($part)) && trim($part) !== $keyword && mb_strlen(trim($part), 'UTF-8') >= 2;
                    });

                    if (count($validParts) >= 2) {
                        // 要求至少匹配两个分词（提高相关性）
                        foreach ($searchFields as $field) {
                            $subQuery->whereOr(function($fieldQuery) use ($validParts, $field) {
                                foreach ($validParts as $part) {
                                    $fieldQuery->where($field, 'like', '%'.trim($part).'%');
                                }
                            });
                        }
                    }
                });
            }
        };
    }

    /**
     * 获取关键词的同义词和相关词
     *
     * @param string $keyword 原始关键词
     * @return array 同义词数组
     */
    protected static function getSynonyms($keyword)
    {
        // 定义同义词映射表
        $synonymMap = [
            // 眼镜相关
            '眼镜' => ['老花镜', '近视镜', '太阳镜', '墨镜', '护目镜'],
            '老花镜' => ['眼镜', '花镜', '老花眼镜', '老人眼镜'],
            '近视镜' => ['眼镜', '近视眼镜'],
            '太阳镜' => ['眼镜', '墨镜', '遮阳镜'],
            '墨镜' => ['眼镜', '太阳镜', '遮阳镜'],

            // 鞋类相关
            '老人鞋' => ['健步鞋', '中老年鞋', '老年鞋', '舒适鞋'],
            '健步鞋' => ['老人鞋', '运动鞋', '休闲鞋'],
            '运动鞋' => ['跑步鞋', '健步鞋', '休闲鞋'],

            // 医疗器械相关
            '轮椅' => ['代步车', '助行器', '行走辅助器'],
            '拐杖' => ['助行器', '手杖', '行走辅助器'],
            '助行器' => ['拐杖', '轮椅', '行走辅助器'],

            // 护理用品相关
            '护理垫' => ['尿垫', '防漏垫', '床垫'],
            '成人纸尿裤' => ['尿不湿', '纸尿片', '护理垫'],
        ];

        $keyword = trim($keyword);
        $synonyms = [];

        // 直接匹配
        if (isset($synonymMap[$keyword])) {
            $synonyms = $synonymMap[$keyword];
        }

        // 模糊匹配（包含关系）
        foreach ($synonymMap as $key => $values) {
            if (strpos($keyword, $key) !== false || strpos($key, $keyword) !== false) {
                $synonyms = array_merge($synonyms, $values);
            }
        }

        // 去重并过滤掉原关键词
        $synonyms = array_unique($synonyms);
        $synonyms = array_filter($synonyms, function($synonym) use ($keyword) {
            return $synonym !== $keyword;
        });

        return array_values($synonyms);
    }

    //检查商品是否正在参加活动
    public static function checkActivity($goods){
        // 获取正在秒杀的时段
        $seckill_time = SeckillGoodsLogic::getSeckillTimeIng();

        if($seckill_time === false) {
            // 不在秒杀时段，直接返回
            return $goods;
        }

        // 判断是否是秒杀中的商品
        $seckill_goods = SeckillGoods::where([
            ['del', '=', 0],
            ['seckill_id', '=', $seckill_time['id']],
            ['goods_id', '=', $goods['id']],
            ['review_status', '=', 1],
        ])->select()->toArray();

        if(!$seckill_goods) {
            // 不是秒杀商品
            return $goods;
        }
        // 判断参与日期是否包含今天
        $flag = false;
        $now = time();
        foreach($seckill_goods as $item) {
            $start_date_time = strtotime($item['start_date'].' ' . $seckill_time['start_time']);
            $end_date_time = strtotime($item['end_date'].' ' . $seckill_time['end_time']);
            if($start_date_time < $now && $end_date_time > $now) {
                $flag = true;
                // 获取该商品的秒杀信息
                $seckill_goods_info = SeckillGoods::where([
                    'goods_id' => $goods['id'],
                    'seckill_id' => $seckill_time['id'],
                    'start_date' => $item['start_date'],
                    'end_date' => $item['end_date'],
                ])->column('goods_id,item_id,price', 'item_id');
                break;
            }
        }

        if($flag === false) {
            // 参与日期不在今天
            return $goods;
        }
        // 确定是秒杀中的商品
        // 先将商品市场价换成原SKU最小价
        $goods['market_price'] = $goods['min_price'];
        // 替换活动价
        foreach($goods['goods_item'] as &$item) {
            // 商品价格替换为最小的秒杀价
            if($goods['min_price'] > $seckill_goods_info[$item['id']]['price']) {
                $goods['min_price'] = $seckill_goods_info[$item['id']]['price'];
            }
            // 原市场价替换为原SKU售价
            $item['market_price'] = $item['price'];
            // SKU替换秒杀价
            $item['price'] = $seckill_goods_info[$item['id']]['price'];
        }
        $today_date = date('Y-m-d');
        $goods['activity'] = [
            'type' => 1,
            'type_desc' => '秒杀商品',
            'end_time' => strtotime($today_date.' '.$seckill_time['end_time'])
        ];
        return $goods;

    }

    /**
     * @notes 获取商品分销信息
     * @param $goodsId
     * @param $userId
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2021/9/6 18:48
     */
    public static function getDistribution($goodsId, $userId)
    {
        $earnings = 0;
        $goods = Goods::findOrEmpty($goodsId)->toArray();
        $distributionGoods = DistributionGoods::where('goods_id', $goodsId)->select()->toArray();
        if(!empty($distributionGoods) && $distributionGoods[0]['is_distribution'] && $distributionGoods[0]['rule'] == 2) {
            foreach($distributionGoods as $item) {
                $earnings = max($earnings, round($goods['max_price'] * $item['first_ratio'] / 100, 2));
                $earnings = max($earnings, round($goods['max_price'] * $item['second_ratio'] / 100, 2));
            }
        }
        if(!empty($distributionGoods) && $distributionGoods[0]['is_distribution'] && $distributionGoods[0]['rule'] == 1) {
            $levels = DistributionLevel::select()->toArray();
            foreach($levels as $item) {
                $earnings = max($earnings, round($goods['max_price'] * $item['first_ratio'] / 100, 2));
                $earnings = max($earnings, round($goods['max_price'] * $item['second_ratio'] / 100, 2));
            }
        }

        // 详情页是否显示佣金
        $isShow = ConfigServer::get('distribution', 'is_show_earnings', 0);
        // 系统总分销开关
        $distributionOpen = ConfigServer::get('distribution', 'is_open', 0);
        // 商家信息-获取商家是否被禁用分销功能(is_distribution)
        $shop = Shop::findOrEmpty($goods['shop_id'])->toArray();

        if ($distributionOpen && $shop['is_distribution'] && $isShow) {
            //详情页佣金可见用户 0-全部用户 1-分销商
            $scope = ConfigServer::get('distribution', 'show_earnings_scope', 0);
            $user = Distribution::where(['user_id' => $userId])->findOrEmpty()->toArray();
            if ($scope && empty($user['is_distribution'])) {
                $isShow = 0;
            }
        } else {
            $isShow = 0;
        }

        return [
            'is_show' => $isShow,
            'earnings' => $earnings
        ];
    }



    /**
     * 集采拼单商品列表
     */
    public static function getpdGoodsList($get)
    {
        // 销售中商品：未删除/审核通过/已上架
        $where = [
            ['del', '=', GoodsEnum::DEL_NORMAL],  // 未删除
            ['status', '=', GoodsEnum::STATUS_SHELVES], // 上架中
            ['audit_status', '=', GoodsEnum::AUDIT_STATUS_OK], // 审核通过
            ['join_jc', '=', 1], // 是否加入拼单集采通过
        ];

        $goods_ids=Db::name('jcai_activity')
            ->where([
                ['status','<>',2],
                ['del','=',0],
            ])
            ->column('goods_id');
        if($goods_ids){
            $where[]=['id','not in',$goods_ids];
        }

        $order = [
            'sort_weight' => 'asc', // 商品权重，数字越小权重越大
            'sort' => 'asc',
            'id' => 'desc'
        ];




        return self::getGoodsListTemplate($where, $order, $get);
    }


    /**
     * 行家严选
     */
    public static  function chooseByExpert($get)
    {
        // 销售中商品：未删除/审核通过/已上架
        $where = [
            ['del', '=', GoodsEnum::DEL_NORMAL],  // 未删除
            ['status', '=', GoodsEnum::STATUS_SHELVES], // 上架中
            ['audit_status', '=', GoodsEnum::AUDIT_STATUS_OK], // 审核通过
            ['join_jc', '=', 0], // 没有加入拼单集采通过
        ];
        $shop_ids=Db::name('shop')->where([
            ['yan_level','>',0],
            ['is_run','=',1],
            ['yan_fee','=', 1]
        ])->order('score desc,return_rate desc')->column('id');

        if(!empty($shop_ids)){
          $shop_ids=implode(',',$shop_ids);
          $get['filed_asc']=' field(shop_id,'.$shop_ids.') ';
        }else{
            $data = [
                'lists'         => [],
                'page_no'       => 0,
                'page_size'     => 0,
                'count'         => 0,
                'more'          => 0
            ];
            return $data;
        }

        $order = [
            'hgou_lv' => 'desc',
            'sales_total' => 'desc', // 虚拟销量倒序
        ];


        return self::getGoodsListTemplate($where, $order, $get);
    }


    /**
     * 居家爆品
     */
    public static  function getHomeHitsList($get)
    {
        // 销售中商品：未删除/审核通过/已上架
        $where = [
            ['del', '=', GoodsEnum::DEL_NORMAL],  // 未删除
            ['status', '=', GoodsEnum::STATUS_SHELVES], // 上架中
            ['audit_status', '=', GoodsEnum::AUDIT_STATUS_OK], // 审核通过
            ['join_jc', '=', 0], // 没有加入拼单集采通过
        ];
        $where[]=['first_cate_id','in',[1,6,12,16,15]];
        $order = [
            'sales_total' => 'desc', // 虚拟销量倒序
            'hgou_lv' => 'desc',
            'clicks' => 'desc', // 虚拟销量倒序
        ];
        return self::getGoodsListTemplate($where, $order, $get);
    }

    /**
     * 找厂家-热销企业-热门工厂精选
     */
    public static  function getHotGoods($get)
    {
        // 销售中商品：未删除/审核通过/已上架
        $where = [
            ['del', '=', GoodsEnum::DEL_NORMAL],  // 未删除
            ['status', '=', GoodsEnum::STATUS_SHELVES], // 上架中
            ['audit_status', '=', GoodsEnum::AUDIT_STATUS_OK], // 审核通过
            ['join_jc', '=', 0], // 没有加入拼单集采通过
        ];
        $order=[];
        $get['is_rand']=1;
        return self::getGoodsListTemplate($where, $order, $get);
    }


    /**
     * 找产品-年度榜
     */
    public static  function getAnnualTopProducts($get)
    {
        // 销售中商品：未删除/审核通过/已上架
        $where = [
            ['del', '=', GoodsEnum::DEL_NORMAL],  // 未删除
            ['status', '=', GoodsEnum::STATUS_SHELVES], // 上架中
            ['audit_status', '=', GoodsEnum::AUDIT_STATUS_OK], // 审核通过
            ['join_jc', '=', 0], // 没有加入拼单集采通过
        ];
        //当前年的开始到结束时间
        $start_time = strtotime(date('Y-01-01'));
        $end_time = time();
        $where_order[]=['o.create_time','between',[$start_time,$end_time]];
        //获取订单id
        $order_ids=OrderGoods::where($where_order)->alias('o')
            ->field('o.goods_id,count(o.goods_num) as num')
            ->LeftJoin('goods s','s.id=o.goods_id')
            ->where([
                ['o.refund_status','=',0],
                ['s.del','=',0],
                ['s.status','=',1],
            ])
            ->group('o.goods_id')
            ->order('num desc')
            ->select()->toArray();
        //更新goods表中的year_sales字段
        foreach ($order_ids as $item){
            $goods=Goods::find($item['goods_id']);
            if(!empty($goods)){
                $goods->year_sales=$item['num'];
                $goods->save();
            }
        }

        $order = [
            'year_sales' => 'desc', // 年销量倒序
            'sales_total' => 'desc', // 虚拟销量倒序
            'hgou_lv' => 'desc',
            'clicks' => 'desc', //点击量
        ];
        return self::getGoodsListTemplate($where, $order, $get);
    } /**
     * 找产品-机构必采
     */
    public static  function getBibuySellers($get)
    {
        // 销售中商品：未删除/审核通过/已上架
        $where = [
            ['del', '=', GoodsEnum::DEL_NORMAL],  // 未删除
            ['status', '=', GoodsEnum::STATUS_SHELVES], // 上架中
            ['audit_status', '=', GoodsEnum::AUDIT_STATUS_OK], // 审核通过
            ['join_jc', '=', 0], // 没有加入拼单集采通过
        ];
        $get['where_raw'] = "FIND_IN_SET(9,`goods_label_top`)";
        $order = [
            'year_sales' => 'desc', // 年销量倒序
            'sales_total' => 'desc', // 虚拟销量倒序
            'hgou_lv' => 'desc',
            'clicks' => 'desc', //点击量
        ];
        return self::getGoodsListTemplate($where, $order, $get);
    }


    /**
     * 集采购-年度榜
     */
    public static  function getPurchaseAnnualRank($get)
    {
        // 销售中商品：未删除/审核通过/已上架
        $where = [
            ['del', '=', GoodsEnum::DEL_NORMAL],  // 未删除
            ['status', '=', GoodsEnum::STATUS_SHELVES], // 上架中
            ['audit_status', '=', GoodsEnum::AUDIT_STATUS_OK], // 审核通过

        ];
        //当前年的开始到结束时间
        $start_time = strtotime(date('Y-01-01'));
        $end_time = time();
        $where_order[]=['o.create_time','between',[$start_time,$end_time]];
        //获取订单id
        $order_ids=OrderGoods::where($where_order)->alias('o')
            ->field('o.goods_id,count(o.goods_num) as num')
            ->LeftJoin('goods s','s.id=o.goods_id')
            ->where([
                ['o.refund_status','=',0],
                ['s.del','=',0],
                ['s.status','=',1],
                ['s.join_jc','=',1],
            ])
            ->group('o.goods_id')
            ->order('num desc')
            ->select()->toArray();
        //更新goods表中的year_sales字段
        foreach ($order_ids as $item){
            $goods=Goods::find($item['goods_id']);
            if(!empty($goods)){
                $goods->year_jc_sales=$item['num'];
                $goods->save();
            }
        }
        $where[]=['join_jc','=',1];
        $goods_ids=Db::name('jcai_activity')
            ->where([
                ['status','<>',2],
                ['del','=',0],
            ])
            ->column('goods_id');
        if($goods_ids){
            $where[]=['id','not in',$goods_ids];
        }
        $order = [
            'year_jc_sales' => 'desc', // 年销量倒序
            'sales_total' => 'desc', // 虚拟销量倒序
            'clicks' => 'desc', //点击量
        ];
        return self::getGoodsListTemplate($where, $order, $get);
    }

    /**
     * 找产品-收藏榜
     */
    public static  function getProductCollectionRanking($get)
    {
        // 销售中商品：未删除/审核通过/已上架
        $where = [
            ['del', '=', GoodsEnum::DEL_NORMAL],  // 未删除
            ['status', '=', GoodsEnum::STATUS_SHELVES], // 上架中
            ['audit_status', '=', GoodsEnum::AUDIT_STATUS_OK], // 审核通过
            ['join_jc', '=', 0], // 没有加入拼单集采通过
        ];

        $goods=Db::name('goods_collect')
            ->field('goods_id,count(id) as num')
            ->where('status',1)
            ->group('goods_id')
            ->order('num desc')
            ->select()->toArray();
       if(!empty($goods)){
           foreach ($goods as $item){
               $goods_id=$item['goods_id'];
               Goods::where('id',$goods_id)->update(['collect_nums'=>$item['num']]);
           }
       }
        $order = [
            'collect_nums' => 'desc', // 年销量倒序
            'sales_total' => 'desc', // 虚拟销量倒序
            'clicks' => 'desc', //点击量
        ];
        return self::getGoodsListTemplate($where, $order, $get);

    }

    /**
     * 找产品-新品上新
     */
    public static function getNewProduct($get)
    {
        // 销售中商品：未删除/审核通过/已上架
        $where = [
            ['del', '=', GoodsEnum::DEL_NORMAL],  // 未删除
            ['status', '=', GoodsEnum::STATUS_SHELVES], // 上架中
            ['audit_status', '=', GoodsEnum::AUDIT_STATUS_OK], // 审核通过
            ['join_jc', '=', 0], // 没有加入拼单集采通过
        ];

        // 使用whereOr查询条件，同时查询最近30天的商品和带有"新品上新"标签的商品
        $whereOr = [
            [
                ['create_time', '>=', strtotime(date('Y-m-d', strtotime("-30 day")))],
                ['create_time', '<', strtotime(date('Y-m-d'))]
            ],
            [
                ['goods_label_top', 'like', '%2%'] // 2是"新品上新"标签的ID
            ]
        ];
        //随机排序
        $order_array=['asc','desc'];
        $order_field_array=['update_time','max_price','sort_weight','create_time','id','name','shop_id','sort'];

        $ar1=rand(0,1);
        $ar2=rand(0,7);

        // 排序规则：先按权重排序，再按创建时间排序
        $order = [
             $order_field_array[$ar2] =>  $order_array[$ar1], // 商品权重，数字越小权重越大
        ];

        $get['whereOr'] = $whereOr;

        return self::getGoodsListTemplate($where, $order, $get);
    }


    /**
     * 找产品-热卖爆品
     */
    public static  function getHotSellers($get)
    {
        // 销售中商品：未删除/审核通过/已上架
        $where = [
            ['del', '=', GoodsEnum::DEL_NORMAL],  // 未删除
            ['status', '=', GoodsEnum::STATUS_SHELVES], // 上架中
            ['audit_status', '=', GoodsEnum::AUDIT_STATUS_OK], // 审核通过
            ['join_jc', '=', 0], // 没有加入拼单集采通过
        ];
        //一个月区间上架的商品


        $order = [
            'sales_total' => 'desc',
        ];
        return self::getGoodsListTemplate($where, $order, $get);
    }


    /**
     * 集采购-集采爆品
     */
    public static  function getpurchaseExplosives($get)
    {
        // 销售中商品：未删除/审核通过/已上架
        $where = [
            ['del', '=', GoodsEnum::DEL_NORMAL],  // 未删除
            ['status', '=', GoodsEnum::STATUS_SHELVES], // 上架中
            ['audit_status', '=', GoodsEnum::AUDIT_STATUS_OK], // 审核通过

        ];
        //一个月区间上架的商品
        $goods_ids=Db::name('jcai_activity')
            ->where([
                ['status','<>',2],
                ['del','=',0],
            ])
            ->column('goods_id');
        if($goods_ids){
            $where[]=['id','not in',$goods_ids];
        }
        $where[]=['join_jc','=',1];
        $order = [
            'sales_total' => 'desc',
        ];
        return self::getGoodsListTemplate($where, $order, $get);
    }


    /**
     * 集采购-推荐榜
     */
    public static  function getRecommendedPurchaseList($get)
    {
        // 销售中商品：未删除/审核通过/已上架
        $where = [
            ['del', '=', GoodsEnum::DEL_NORMAL],  // 未删除
            ['status', '=', GoodsEnum::STATUS_SHELVES], // 上架中
            ['audit_status', '=', GoodsEnum::AUDIT_STATUS_OK], // 审核通过
        ];
        //一个月区间上架的商品
        $goods_ids=Db::name('jcai_activity')
            ->where([
                ['status','<>',2],
                ['del','=',0],
            ])
            ->column('goods_id');
        if($goods_ids){
            $where[]=['id','not in',$goods_ids];
        }
        $where[]=['join_jc','=',1];
        $order = [
            'reshare_nums' => 'desc',
            'sales_total' => 'desc',
            'clicks' => 'desc',
            'share_nums' => 'desc',
        ];
        return self::getGoodsListTemplate($where, $order, $get);
    }



    public static function calculateRepurchaseRate($goods_id)
    {

        // 或者如果不使用join，先获取order_goods表中的order_id，再查询order表

        $orderIds = DB::name('order_goods')
            ->where([
                ['goods_id','=',$goods_id],
                ['refund_status','=',0]
            ])
            ->group('order_id')
            ->column('order_id');
        if (empty($orderIds)) {
            return 0.00; // 如果没有任何订单包含该商品，则回购率为0
        }
        $user_id = DB::name('order')
            ->where([
                ['id','in',$orderIds],
                ['refund_status','=',0],
                ['pay_status','=',1],
//                ['order_status','=',3]
            ])
            ->group('user_id')
            ->count('user_id');


        if (empty($user_id)) {
            return 0.00; // 如果没有客户购买过该商品，则回购率为0
        }
        $user_ids = DB::name('order')
            ->where([
                ['id','in',$orderIds],
                ['refund_status','=',0],
                ['pay_status','=',1],
//                ['order_status','=',3]
            ])
            ->group('user_id')
            ->having('COUNT(user_id) > 1')
            ->count('user_id');

        if (empty($user_ids)) {
            return 0.00; // 如果没有客户购买过该商品，则回购率为0
        }
        // 计算回购率
        $repurchaseRate = $user_ids / $user_id;

        // 保留两位小数
        return round($repurchaseRate, 2);
    }


    /*
     * 智能推荐商品 - 多维度推荐算法
     * 基于用户行为、商品分类、语义分析、协同过滤等多种策略
     */
    public static function getRecommendGoods($get){
        $userId = $get['user_id'];

        try {
            // 1. 获取用户行为数据（实时，不使用缓存）
            $behaviorData = self::getUserBehaviorData($userId);

            // 2. 合并“即时信号”（无需等待入库）：当前搜索词/当前查看商品
            if (!empty($get['keyword'])) {
                array_unshift($behaviorData['search_history'], [
                    'keyword' => trim($get['keyword']),
                    'update_time' => time(),
                    'count' => 1,
                ]);
            }
            if (!empty($get['goods_id'])) {
                $g = Goods::where([['id','=',$get['goods_id']],['del','=',GoodsEnum::DEL_NORMAL]])
                    ->field('id,first_cate_id,second_cate_id,third_cate_id,shop_id')
                    ->find();
                if ($g) {
                    array_unshift($behaviorData['click_history'], [
                        'goods_id' => (int)$g['id'],
                        'create_time' => time(),
                        'first_cate_id' => (int)$g['first_cate_id'],
                        'second_cate_id' => (int)$g['second_cate_id'],
                        'third_cate_id' => (int)$g['third_cate_id'],
                        'shop_id' => (int)$g['shop_id'],
                    ]);
                }
            }

            // 3. 分析用户偏好（基于最新的行为数据）
            $userPreferences = self::analyzeUserPreferences($behaviorData);

            // 4. 多策略推荐
            $recommendations = [];

            // 获取推荐配置
            $config = Config::get('recommend', []);
            $weights = $config['strategy_weights'] ?? [];
            $limits = $config['recommendation_limits'] ?? [];

            // 获取有效店铺ID列表
            $validShopIds = $get['valid_shop_ids'] ?? [];

            // 基于最近行为（搜索/点击/收藏）的强力推荐，优先同类目相似商品
            if (!empty($behaviorData)) {
                $recentMap = self::getRecentBehaviorRecommendations($behaviorData, $limits['recent_behavior'] ?? 80);
                if (!empty($recentMap)) {
                    $recommendations = array_merge($recommendations, self::addWeightsMap($recentMap, $weights['recent_behavior'] ?? 1.0));
                }
            }

            // 实时关联推荐（当前搜索词的强相关商品，优先级最高）
            if (!empty($get['keyword'])) {
                $realtimeRecs = self::getRealtimeAssociatedRecommendations(trim($get['keyword']), $limits['realtime_associated'] ?? 60, $validShopIds);
                $recommendations = array_merge($recommendations, self::addWeight($realtimeRecs, $weights['realtime_associated'] ?? 1.5));
            }

            // 基于语义的推荐（用户最近搜索）
            $semanticRecs = self::getSemanticBasedRecommendations($behaviorData['search_history'] ?? [], $limits['semantic_based'] ?? 30);
            $recommendations = array_merge($recommendations, self::addWeight($semanticRecs, $weights['semantic_based'] ?? 0.25));

            // 基于分类的推荐（由点击/购买/收藏偏好推断）
            $categoryRecs = self::getCategoryBasedRecommendations($userPreferences, $limits['category_based'] ?? 50);
            $recommendations = array_merge($recommendations, self::addWeight($categoryRecs, $weights['category_based'] ?? 0.4));

            // 基于协同过滤的推荐
            $collaborativeRecs = self::getCollaborativeRecommendations($userId, $limits['collaborative'] ?? 25);
            $recommendations = array_merge($recommendations, self::addWeight($collaborativeRecs, $weights['collaborative'] ?? 0.2));

            // 基于热度的推荐（实时，不使用缓存）
            $hotRecs = self::getHotRecommendations($limits['hot_products'] ?? 15);
            $recommendations = array_merge($recommendations, self::addWeight($hotRecs, $weights['hot_products'] ?? 0.1));

            // 新品推荐（实时，不使用缓存）
            $newRecs = self::getNewProductRecommendations($limits['new_products'] ?? 10);
            $recommendations = array_merge($recommendations, self::addWeight($newRecs, $weights['new_products'] ?? 0.05));

            // 4. 合并、去重、排序（传递用户ID用于抖动计算）
            $userPreferences['user_id'] = $userId;
            $finalRecommendations = self::mergeAndRankRecommendations($recommendations, $userPreferences);

            // 5. 硬置顶：优先将“点击/搜索”匹配的商品置顶，数量尽可能多
            $finalLimit = Config::get('recommend.recommendation_limits.final_result', 100);
            $pinCfg = Config::get('recommend.pin_top', []);
            $enablePin = $pinCfg['enable'] ?? true;
            $pinnedLimit = $pinCfg['pinned_limit'] ?? $finalLimit;
            $preferClick = $pinCfg['prefer_click_over_search'] ?? true;

            $pinned = [];
            if ($enablePin) {
                $clickPinned = [];
                $searchPinned = [];
                if (!empty($get['goods_id'])) {
                    $clickPinned = self::buildPinnedFromClick((int)$get['goods_id'], $pinnedLimit, $pinCfg);
                }
                if (!empty($get['keyword'])) {
                    $kw = trim((string)$get['keyword']);
                    if ($kw !== '') {
                        $searchPinned = self::buildPinnedFromSearch($kw, $pinnedLimit);
                    }
                }
                $pinned = $preferClick
                    ? array_values(array_unique(array_merge($clickPinned, $searchPinned)))
                    : array_values(array_unique(array_merge($searchPinned, $clickPinned)));
                if (count($pinned) > $pinnedLimit) {
                    $pinned = array_slice($pinned, 0, $pinnedLimit);
                }
            }

            // 6. 生成最终的排序字段：Pinned 在前，Rest 在后
            $rest = array_values(array_diff($finalRecommendations, $pinned));
            $rest = array_slice($rest, 0, max(0, $finalLimit - count($pinned)));
            $finalIds = array_merge($pinned, $rest);
            //$finalIds 乱序
          
            $result = self::generateSortField($finalIds);

            return $result;

        } catch (\Exception $e) {
            // 降级策略：返回基于热度的随机排序
            var_dump($e->getMessage().'-'.$e->getLine());die;
            \think\facade\Log::error('推荐算法执行失败: ' . $e->getMessage());
            return 'RAND()';
        }
    }


    /**
     * 获取用户行为数据
     * @param int $userId 用户ID
     * @return array 用户行为数据
     */
    private static function getUserBehaviorData($userId)
    {
        $behaviorData = [
            'search_history' => [],
            'click_history' => [],
            'purchase_history' => [],
            'collect_history' => [],
            'footprint_history' => []
        ];

        try {
            // 获取搜索历史（最近30天，最多20条）
            $searchHistory = SearchRecord::where(['user_id' => $userId, 'del' => 0])
                ->where('update_time', '>=', time() - 30 * 24 * 3600)
                ->field('keyword,update_time,count')
                ->order('update_time desc')
                ->limit(20)
                ->select()
                ->toArray();
            $behaviorData['search_history'] = $searchHistory;

            // 获取点击历史（最近30天，最多50条）
            $clickHistory = GoodsClick::alias('gc')
                ->leftJoin('goods g', 'g.id = gc.goods_id')
                ->where(['gc.user_id' => $userId])
                ->where('gc.create_time', '>=', time() - 30 * 24 * 3600)
                ->where(['g.del' => 0, 'g.status' => 1, 'g.audit_status' => 1])
                ->field('gc.goods_id,gc.create_time,g.third_cate_id,g.second_cate_id,g.first_cate_id,g.shop_id')
                ->order('gc.create_time desc')
                ->limit(50)
                ->select()
                ->toArray();
            $behaviorData['click_history'] = $clickHistory;

            // 获取购买历史（最近90天）
            $purchaseHistory = Db::name('order_goods')
                ->alias('og')
                ->leftJoin('order o', 'o.id = og.order_id')
                ->leftJoin('goods g', 'g.id = og.goods_id')
                ->where(['o.user_id' => $userId])
                ->where('o.create_time', '>=', time() - 90 * 24 * 3600)
                ->where(['g.del' => 0])
                ->field('og.goods_id,o.create_time,g.third_cate_id,g.second_cate_id,g.first_cate_id,og.total_price')
                ->order('o.create_time desc')
                ->limit(30)
                ->select()
                ->toArray();
            $behaviorData['purchase_history'] = $purchaseHistory;

            // 获取收藏历史（最近60天）
            $collectHistory = GoodsCollect::alias('gc')
                ->leftJoin('goods g', 'g.id = gc.goods_id')
                ->where(['gc.user_id' => $userId, 'gc.status' => 1])
                ->where('gc.create_time', '>=', time() - 60 * 24 * 3600)
                ->where(['g.del' => 0, 'g.status' => 1, 'g.audit_status' => 1])
                ->field('gc.goods_id,gc.create_time,g.third_cate_id,g.second_cate_id,g.first_cate_id')
                ->order('gc.create_time desc')
                ->limit(30)
                ->select()
                ->toArray();
            $behaviorData['collect_history'] = $collectHistory;

            // 获取浏览足迹（最近30天，最多30条）
            $footprintHistory = Db::name('goods_footprint')
                ->alias('gf')
                ->leftJoin('goods g', 'g.id = gf.goods_id')
                ->where(['gf.user_id' => $userId])
                ->where('gf.update_time', '>=', time() - 30 * 24 * 3600)
                ->where(['g.del' => 0, 'g.status' => 1, 'g.audit_status' => 1])
                ->field('gf.goods_id,gf.update_time,gf.nums,g.third_cate_id,g.second_cate_id,g.first_cate_id')
                ->order('gf.update_time desc')
                ->limit(30)
                ->select()
                ->toArray();
            $behaviorData['footprint_history'] = $footprintHistory;

        } catch (\Exception $e) {
            \think\facade\Log::error('获取用户行为数据失败: ' . $e->getMessage());
        }

        return $behaviorData;
    }

    /**
     * 分析用户偏好
     * @param array $behaviorData 用户行为数据
     * @return array 用户偏好分析结果
     */
    private static function analyzeUserPreferences($behaviorData)
    {
        $preferences = [
            'categories' => [],
            'price_range' => [],
            'shops' => [],
            'keywords' => [],
            'time_weights' => []
        ];

        $now = time();

        // 分析分类偏好
        $categoryWeights = [];

        // 从点击历史分析分类偏好
        foreach ($behaviorData['click_history'] as $click) {
            $timeWeight = self::calculateTimeWeight($click['create_time'], $now);
            $categories = [$click['first_cate_id'], $click['second_cate_id'], $click['third_cate_id']];

            foreach ($categories as $level => $cateId) {
                if ($cateId > 0) {
                    $key = "level_" . ($level + 1) . "_" . $cateId;
                    $categoryWeights[$key] = ($categoryWeights[$key] ?? 0) + $timeWeight;
                }
            }
        }

        // 从购买历史分析分类偏好（权重适中）
        foreach ($behaviorData['purchase_history'] as $purchase) {
            $timeWeight = self::calculateTimeWeight($purchase['create_time'], $now) * 1.8; // 购买权重适中，不超过搜索和浏览
            $categories = [$purchase['first_cate_id'], $purchase['second_cate_id'], $purchase['third_cate_id']];

            foreach ($categories as $level => $cateId) {
                if ($cateId > 0) {
                    $key = "level_" . ($level + 1) . "_" . $cateId;
                    $categoryWeights[$key] = ($categoryWeights[$key] ?? 0) + $timeWeight;
                }
            }
        }

        // 从收藏历史分析分类偏好（权重中等）
        foreach ($behaviorData['collect_history'] as $collect) {
            $timeWeight = self::calculateTimeWeight($collect['create_time'], $now) * 1.6; // 略微提升收藏权重
            $categories = [$collect['first_cate_id'], $collect['second_cate_id'], $collect['third_cate_id']];

            foreach ($categories as $level => $cateId) {
                if ($cateId > 0) {
                    $key = "level_" . ($level + 1) . "_" . $cateId;
                    $categoryWeights[$key] = ($categoryWeights[$key] ?? 0) + $timeWeight;
                }
            }
        }

        // 排序并取前10个分类偏好
        arsort($categoryWeights);
        $preferences['categories'] = array_slice($categoryWeights, 0, 10, true);

        // 分析关键词偏好（提升搜索行为权重）
        $keywordWeights = [];
        foreach ($behaviorData['search_history'] as $search) {
            $timeWeight = self::calculateTimeWeight($search['update_time'], $now);
            $weight = $timeWeight * $search['count'] * 1.3; // 提升搜索行为权重，考虑搜索次数
            $keywordWeights[$search['keyword']] = ($keywordWeights[$search['keyword']] ?? 0) + $weight;
        }
        arsort($keywordWeights);
        $preferences['keywords'] = array_slice($keywordWeights, 0, 10, true);

        return $preferences;
    }

    /**
     * 计算时间权重 - 支持精细化分钟/小时/天分档
     * @param int $actionTime 行为发生时间
     * @param int $currentTime 当前时间
     * @return float 时间权重
     */
    private static function calculateTimeWeight($actionTime, $currentTime)
    {
        $config = Config::get('recommend.time_weights', []);

        // 如果$actionTime 是时间格式2025-08-08 18:06:27,转换为时间戳
        if(str_replace(' ', '-', $actionTime) != $actionTime){
            $actionTime = strtotime($actionTime);
        }

        $timeDiff = $currentTime - $actionTime; // 时间差（秒）

        // 获取精细化时间权重配置
        $minuteWeights = $config['minute_weights'] ?? [];
        $hourWeights = $config['hour_weights'] ?? [];
        $dayWeights = $config['day_weights'] ?? [];

        // 分钟级别权重（最高优先级）
        if ($timeDiff <= 1200) { // 20分钟内
            return $minuteWeights['20_minutes'] ?? 1.5;
        } elseif ($timeDiff <= 3600) { // 1小时内
            return $hourWeights['1_hour'] ?? 1.3;
        } elseif ($timeDiff <= 86400) { // 24小时内
            return $dayWeights['24_hours'] ?? 1.1;
        }

        // 天级别权重（兼容原有逻辑）
        $daysDiff = $timeDiff / (24 * 3600);
        $recentDays = $config['recent_days'] ?? 7;
        $mediumDays = $config['medium_days'] ?? 30;
        $longDays = $config['long_days'] ?? 90;
        $weights = $config['weights'] ?? [];

        if ($daysDiff <= $recentDays) {
            return $weights['recent'] ?? 1.0;
        } elseif ($daysDiff <= $mediumDays) {
            return $weights['medium'] ?? 0.7;
        } elseif ($daysDiff <= $longDays) {
            return $weights['long'] ?? 0.3;
        } else {
            return $weights['very_long'] ?? 0.1;
        }
    }

    /**
     * 基于分类的推荐
     * @param array $userPreferences 用户偏好
     * @param int $limit 推荐数量限制
     * @return array 推荐商品ID列表
     */
    private static function getCategoryBasedRecommendations($userPreferences, $limit = 50)
    {
        $recommendations = [];

        if (empty($userPreferences['categories'])) {
            return $recommendations;
        }

        $baseWhere = [
            ['del', '=', GoodsEnum::DEL_NORMAL],
            ['status', '=', GoodsEnum::STATUS_SHELVES],
            ['audit_status', '=', GoodsEnum::AUDIT_STATUS_OK],
            ['shop_id', 'not in', self::filterShopsIds()]
        ];

        foreach ($userPreferences['categories'] as $categoryKey => $weight) {
            // 解析分类信息
            $parts = explode('_', $categoryKey);
            if (count($parts) !== 3) continue;

            $level = $parts[1];
            $cateId = $parts[2];

            // 根据分类级别构建查询条件
            $categoryWhere = $baseWhere;
            switch ($level) {
                case '1':
                    $categoryWhere[] = ['first_cate_id', '=', $cateId];
                    break;
                case '2':
                    $categoryWhere[] = ['second_cate_id', '=', $cateId];
                    break;
                case '3':
                    $categoryWhere[] = ['third_cate_id', '=', $cateId];
                    break;
                default:
                    continue 2;
            }

            // 获取该分类下的商品，按销量和评分排序
            $categoryGoods = Goods::where($categoryWhere)
                ->field('id,sales_actual,sales_virtual,clicks')
                ->orderRaw('(sales_actual + sales_virtual) desc')
                ->order([
                    'clicks' => 'desc',
                    'id' => 'desc'
                ])
                ->limit(20)
                ->column('id');

            // 为每个商品添加权重
            foreach ($categoryGoods as $goodsId) {
                if (!isset($recommendations[$goodsId])) {
                    $recommendations[$goodsId] = 0;
                }
                $recommendations[$goodsId] += $weight;
            }
        }

        // 按权重排序并限制数量
        arsort($recommendations);
        return array_slice(array_keys($recommendations), 0, $limit);
    }

    /**
     * 基于语义的推荐
     * @param array $searchHistory 搜索历史
     * @param int $limit 推荐数量限制
     * @return array 推荐商品ID列表
     */
    private static function getSemanticBasedRecommendations($searchHistory, $limit = 30)
    {
        $recommendations = [];

        if (empty($searchHistory)) {
            return $recommendations;
        }

        $baseWhere = [
            ['del', '=', GoodsEnum::DEL_NORMAL],
            ['status', '=', GoodsEnum::STATUS_SHELVES],
            ['audit_status', '=', GoodsEnum::AUDIT_STATUS_OK],
            ['shop_id', 'not in', self::filterShopsIds()]
        ];

        foreach ($searchHistory as $search) {
            $keyword = $search['keyword'];
            $searchWeight = $search['count'];

            // 使用阿里云NLP进行语义分析
            try {
                $nlpResult = Alisegment($keyword);
                $words = $nlpResult['words'] ?? [$keyword];
            } catch (\Exception $e) {
                // 如果NLP分析失败，使用简单分词
                $words = [$keyword];
            }

            // 构建搜索条件
            $whereOr = [];
            foreach ($words as $word) {
                if (mb_strlen($word, 'UTF-8') >= 2) {
                    $whereOr[] = ['name|remark|content', 'like', '%' . trim($word) . '%'];
                }
            }

            if (!empty($whereOr)) {
                $goodsIds = Goods::where($baseWhere)
                    ->where(function ($query) use ($whereOr) {
                        $query->whereOr($whereOr);
                    })
                    ->orderRaw('(sales_actual + sales_virtual) desc')
                    ->order([
                        'clicks' => 'desc'
                    ])
                    ->limit(15)
                    ->column('id');

                // 为每个商品添加权重
                foreach ($goodsIds as $goodsId) {
                    if (!isset($recommendations[$goodsId])) {
                        $recommendations[$goodsId] = 0;
                    }
                    $recommendations[$goodsId] += $searchWeight;
                }
            }
        }

        // 按权重排序并限制数量
        arsort($recommendations);
        return array_slice(array_keys($recommendations), 0, $limit);
    }

    /**
     * 基于协同过滤的推荐
     * @param int $userId 用户ID
     * @param int $limit 推荐数量限制
     * @return array 推荐商品ID列表
     */
    private static function getCollaborativeRecommendations($userId, $limit = 25)
    {
        $recommendations = [];

        try {
            // 找到与当前用户有相似行为的用户
            $similarUsers = self::findSimilarUsers($userId, 10);

            if (empty($similarUsers)) {
                return $recommendations;
            }

            $baseWhere = [
                ['g.del', '=', GoodsEnum::DEL_NORMAL],
                ['g.status', '=', GoodsEnum::STATUS_SHELVES],
                ['g.audit_status', '=', GoodsEnum::AUDIT_STATUS_OK],
                ['g.shop_id', 'not in', self::filterShopsIds()]
            ];

            // 获取相似用户喜欢的商品
            foreach ($similarUsers as $similarUserId => $similarity) {
                // 获取相似用户最近的点击和收藏
                $userGoods = GoodsClick::alias('gc')
                    ->leftJoin('goods g', 'g.id = gc.goods_id')
                    ->where(['gc.user_id' => $similarUserId])
                    ->where('gc.create_time', '>=', time() - 30 * 24 * 3600)
                    ->where($baseWhere)
                    ->group('gc.goods_id')
                    ->order('count(*) desc')
                    ->limit(10)
                    ->column('gc.goods_id');

                foreach ($userGoods as $goodsId) {
                    if (!isset($recommendations[$goodsId])) {
                        $recommendations[$goodsId] = 0;
                    }
                    $recommendations[$goodsId] += $similarity;
                }
            }

            // 按权重排序并限制数量
            arsort($recommendations);
            return array_slice(array_keys($recommendations), 0, $limit);

        } catch (\Exception $e) {
            \think\facade\Log::error('协同过滤推荐失败: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * 找到相似用户
     * @param int $userId 当前用户ID
     * @param int $limit 相似用户数量限制
     * @return array 相似用户列表
     */
    private static function findSimilarUsers($userId, $limit = 10)
    {
        $similarUsers = [];

        try {
            // 获取当前用户最近点击的商品分类
            $userCategories = GoodsClick::alias('gc')
                ->leftJoin('goods g', 'g.id = gc.goods_id')
                ->where(['gc.user_id' => $userId])
                ->where('gc.create_time', '>=', time() - 30 * 24 * 3600)
                ->where(['g.del' => 0])
                ->group('g.third_cate_id')
                ->column('g.third_cate_id');

            if (empty($userCategories)) {
                return $similarUsers;
            }

            // 找到点击相同分类商品的其他用户
            $otherUsers = GoodsClick::alias('gc')
                ->leftJoin('goods g', 'g.id = gc.goods_id')
                ->where(['g.third_cate_id' => ['in', $userCategories]])
                ->where('gc.user_id', '<>', $userId)
                ->where('gc.create_time', '>=', time() - 30 * 24 * 3600)
                ->group('gc.user_id')
                ->field('gc.user_id, count(*) as common_clicks')
                ->having('common_clicks >= 3') // 至少有3个共同点击
                ->order('common_clicks desc')
                ->limit($limit)
                ->select()
                ->toArray();

            foreach ($otherUsers as $user) {
                // 简单的相似度计算：共同点击数 / 10
                $similarity = min($user['common_clicks'] / 10, 1.0);
                $similarUsers[$user['user_id']] = $similarity;
            }

        } catch (\Exception $e) {
            \think\facade\Log::error('查找相似用户失败: ' . $e->getMessage());
        }

        return $similarUsers;
    }

    /**
     * 基于热度的推荐
     * @param int $limit 推荐数量限制
     * @return array 推荐商品ID列表
     */
    private static function getHotRecommendations($limit = 15)
    {
        $baseWhere = [
            ['del', '=', GoodsEnum::DEL_NORMAL],
            ['status', '=', GoodsEnum::STATUS_SHELVES],
            ['audit_status', '=', GoodsEnum::AUDIT_STATUS_OK],
            ['shop_id', 'not in', self::filterShopsIds()]
        ];

        $hotGoods = Goods::where($baseWhere)
            ->orderRaw('(sales_actual + sales_virtual) desc')
            ->orderRaw('(clicks + clicks_virtual) desc')
            ->order([
                'id' => 'desc'
            ])
            ->limit(50)
            ->column('id');

        return array_slice($hotGoods, 0, $limit);
    }

    /**
     * 新品推荐
     * @param int $limit 推荐数量限制
     * @return array 推荐商品ID列表
     */
    private static function getNewProductRecommendations($limit = 10)
    {
        $baseWhere = [
            ['del', '=', GoodsEnum::DEL_NORMAL],
            ['status', '=', GoodsEnum::STATUS_SHELVES],
            ['audit_status', '=', GoodsEnum::AUDIT_STATUS_OK],
            ['shop_id', 'not in', self::filterShopsIds()],
            ['create_time', '>=', time() - 30 * 24 * 3600] // 最近30天的新品
        ];

        $newGoods = Goods::where($baseWhere)
            ->orderRaw('(sales_actual + sales_virtual) desc')
            ->order([
                'create_time' => 'desc'
            ])
            ->limit(30)
            ->column('id');

        return array_slice($newGoods, 0, $limit);
    }

    /**
     * 为推荐结果添加权重
     * @param array $goodsIds 商品ID列表
     * @param float $weight 权重值
     * @return array 带权重的推荐结果
     */
    private static function addWeight($goodsIds, $weight)
    {
        $weightedResults = [];
        foreach ($goodsIds as $goodsId) {
            $weightedResults[] = [
                'goods_id' => $goodsId,
                'weight' => $weight,
                'source' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 2)[1]['function'] ?? 'unknown'
            ];
        }
        return $weightedResults;
    }

    /**
     * 将 goods_id => 分数 的映射转换为带权重的推荐条目
     * @param array $weightsMap [goods_id => score]
     * @param float $baseWeight 基础权重（会与score相乘）
     * @return array
     */
    private static function addWeightsMap(array $weightsMap, float $baseWeight = 1.0): array
    {
        $results = [];
        foreach ($weightsMap as $gid => $score) {
            $results[] = [
                'goods_id' => $gid,
                'weight'   => max(0, $score) * max(0, $baseWeight),
                'source'   => 'recent_behavior'
            ];
        }
        return $results;
    }

    /**
     * 基于最近行为（搜索/点击/收藏/足迹）的强力推荐
     * 规则：
     *  - 最近点击/收藏的商品：优先同三/f二/一分类、同店铺商品；原商品本身也可加入
     *  - 最近一次搜索：用关键字匹配商品名称/备注/详情
     *  - 时间越近，分数越高
     * 返回：goods_id => score 映射，score为相对值
     */
    private static function getRecentBehaviorRecommendations(array $behaviorData, int $limit = 80): array
    {
        $now = time();
        $result = [];

        // 最近点击：同类目/同店铺尽可能多，并将当前商品本身置前
        $lastClick = $behaviorData['click_history'][0] ?? null;
        if ($lastClick) {
            $timeScore = self::calculateTimeWeight($lastClick['create_time'] ?? $now, $now);
            $cateIds = [
                'third'  => $lastClick['third_cate_id'] ?? 0,
                'second' => $lastClick['second_cate_id'] ?? 0,
                'first'  => $lastClick['first_cate_id'] ?? 0,
            ];
            $shopId = $lastClick['shop_id'] ?? 0;

            $baseWhere = [
                ['del', '=', GoodsEnum::DEL_NORMAL],
                ['status', '=', GoodsEnum::STATUS_SHELVES],
                ['audit_status', '=', GoodsEnum::AUDIT_STATUS_OK],
                ['shop_id', 'not in', self::filterShopsIds()]
            ];

            // 先三层、再二层、再一层
            $collected = [];
            if (!empty($cateIds['third'])) {
                $collected = array_merge($collected, Goods::where($baseWhere)
                    ->where('third_cate_id', $cateIds['third'])
                    ->order([
                        'sales_actual' => 'desc',
                        'clicks'      => 'desc',
                        'id'                           => 'desc'
                    ])->limit($limit)->column('id'));
            }
            if (count($collected) < $limit && !empty($cateIds['second'])) {
                $collected = array_merge($collected, Goods::where($baseWhere)
                    ->where('second_cate_id', $cateIds['second'])
                    ->order([
                        'sales_actual' => 'desc',
                        'clicks'      => 'desc',
                    ])->limit($limit - count($collected))->column('id'));
            }
            if (count($collected) < $limit && !empty($cateIds['first'])) {
                $collected = array_merge($collected, Goods::where($baseWhere)
                    ->where('first_cate_id', $cateIds['first'])
                    ->order([
                       'sales_actual' => 'desc',
                        'clicks'      => 'desc',
                    ])->limit($limit - count($collected))->column('id'));
            }

            // 同店铺补充
            if ($shopId && count($collected) < $limit) {
                $collected = array_merge($collected, Goods::where($baseWhere)
                    ->where('shop_id', $shopId)
                    ->order('id', 'desc')
                    ->limit(max(0, $limit - count($collected)))
                    ->column('id'));
            }

            // 本商品置前
            if (!empty($lastClick['goods_id'])) {
                array_unshift($collected, (int)$lastClick['goods_id']);
            }

            // 赋分：点击基础分提升（5.0）* 时间权重，并做轻微衰减
            // 提升点击行为的基础权重，体现浏览行为的重要性
            $behaviorConfig = Config::get('recommend.behavior_weights', []);
            $clickWeight = $behaviorConfig['click'] ?? 5.0;
            $base = $clickWeight * $timeScore;
            foreach ($collected as $idx => $gid) {
                $score = $base * (1 - min($idx, 50) * 0.01);
                $result[$gid] = max($result[$gid] ?? 0, $score);
            }
        }

        // 最近收藏：同三类目为主
        $lastCollect = $behaviorData['collect_history'][0] ?? null;
        if ($lastCollect) {
            $timeScore = self::calculateTimeWeight($lastCollect['create_time'] ?? $now, $now);
            $cateId = (int)($lastCollect['third_cate_id'] ?? 0);
            if ($cateId) {
                $baseWhere = [
                    ['del', '=', GoodsEnum::DEL_NORMAL],
                    ['status', '=', GoodsEnum::STATUS_SHELVES],
                    ['audit_status', '=', GoodsEnum::AUDIT_STATUS_OK],
                    ['shop_id', 'not in', self::filterShopsIds()]
                ];
                $collected = Goods::where($baseWhere)
                    ->where('third_cate_id', $cateId)
                    ->order([
                        'sales_actual' => 'desc',
                        'clicks'      => 'desc'
                    ])->limit($limit)->column('id');
                $behaviorConfig = Config::get('recommend.behavior_weights', []);
                $collectWeight = $behaviorConfig['collect'] ?? 3.8;
                $base = $collectWeight * $timeScore;
                foreach ($collected as $idx => $gid) {
                    $score = $base * (1 - min($idx, 50) * 0.01);
                    $result[$gid] = max($result[$gid] ?? 0, $score);
                }
            }
        }

        // 最近搜索：原词匹配优先，分词兜底
        $lastSearch = $behaviorData['search_history'][0] ?? null;
        if ($lastSearch) {
            $keyword = trim((string)($lastSearch['keyword'] ?? ''));
            if ($keyword !== '') {
                $timeScore = self::calculateTimeWeight($lastSearch['update_time'] ?? $now, $now);
                $baseWhere = [
                    ['del', '=', GoodsEnum::DEL_NORMAL],
                    ['status', '=', GoodsEnum::STATUS_SHELVES],
                    ['audit_status', '=', GoodsEnum::AUDIT_STATUS_OK],
                    ['shop_id', 'not in', self::filterShopsIds()]
                ];
                $whereOr = [['name|remark|content', 'like', "%{$keyword}%"]];
                try {
                    $nlp = Alisegment($keyword);
                    $words = $nlp['words'] ?? [$keyword];
                    $tmp = [];
                    foreach ($words as $w) {
                        if (mb_strlen($w, 'UTF-8') >= 2) {
                            $tmp[] = ['name|remark|content', 'like', "%".trim($w)."%"];
                        }
                    }
                    if (!empty($tmp)) { $whereOr = $tmp; }
                } catch (\Exception $e) { /* fallback 使用原词 */ }

                $goodsIds = Goods::where($baseWhere)
                    ->where(function ($query) use ($whereOr) { $query->whereOr($whereOr); })
                    ->order([
                        'sales_actual' => 'desc',
                        'clicks'      => 'desc'
                    ])->limit($limit)->column('id');
                $behaviorConfig = Config::get('recommend.behavior_weights', []);
                $searchWeight = $behaviorConfig['search'] ?? 4.5; // 提升搜索权重，体现搜索行为的重要性
                $base = $searchWeight * $timeScore;
                foreach ($goodsIds as $idx => $gid) {
                    $score = $base * (1 - min($idx, 50) * 0.01);
                    $result[$gid] = max($result[$gid] ?? 0, $score);
                }
            }
        }

        if (count($result) > $limit) {
            arsort($result);
            $result = array_slice($result, 0, $limit, true);
        }
        return $result;
    }


    /**
     * 根据“点击的商品”构建硬置顶列表（点击优先）
     * 规则：当前商品置于列表首位；再同三/二/一分类；再同店铺；尽可能多，按销量/热度降序
     */
    private static function buildPinnedFromClick(int $goodsId, int $limit, array $pinCfg = []): array
    {
        $baseWhere = [
            ['del', '=', GoodsEnum::DEL_NORMAL],
            ['status', '=', GoodsEnum::STATUS_SHELVES],
            ['audit_status', '=', GoodsEnum::AUDIT_STATUS_OK],
            ['shop_id', 'not in', self::filterShopsIds()],
        ];
        $g = Goods::where([['id','=',$goodsId]])
            ->field('id,first_cate_id,second_cate_id,third_cate_id,shop_id')
            ->find();
        if (!$g) { return []; }

        $ids = [(int)$g['id']];
        $collected = [];

        $order = [
            'id'                           => 'desc'
        ];

        // 同三、二、一分类
        foreach ([
            ['third_cate_id', (int)$g['third_cate_id']],
            ['second_cate_id', (int)$g['second_cate_id']],
            ['first_cate_id', (int)$g['first_cate_id']],
        ] as [$field, $val]) {
            if ($val) {
                $need = max(0, $limit - count($ids) - count($collected));
                if ($need <= 0) break;
                $collected = array_merge($collected, Goods::where($baseWhere)
                    ->where($field, $val)
                    ->where('id','<>',(int)$g['id'])
                    ->orderRaw('(sales_actual + sales_virtual) desc')
                    ->orderRaw('(clicks + clicks_virtual) desc')
                    ->order($order)
                    ->limit($need)
                    ->column('id'));
            }
        }

        // 同店铺
        if ((int)$g['shop_id'] && count($ids) + count($collected) < $limit) {
            $need = max(0, $limit - count($ids) - count($collected));
            $collected = array_merge($collected, Goods::where($baseWhere)
                ->where('shop_id', (int)$g['shop_id'])
                ->where('id','<>',(int)$g['id'])
                ->order('id', 'desc')
                ->limit($need)
                ->column('id'));
        }

        $out = array_values(array_unique(array_merge($ids, $collected)));
        return array_slice($out, 0, $limit);
    }

    /**
     * 根据“搜索关键字”构建硬置顶列表（搜索优先）
     * 规则：优先原词 LIKE 匹配；可尝试分词；尽可能多，按销量/热度降序
     */
    private static function buildPinnedFromSearch(string $keyword, int $limit): array
    {
        $keyword = trim($keyword);
        if ($keyword === '') return [];

        $baseWhere = [
            ['del', '=', GoodsEnum::DEL_NORMAL],
            ['status', '=', GoodsEnum::STATUS_SHELVES],
            ['audit_status', '=', GoodsEnum::AUDIT_STATUS_OK],
            ['shop_id', 'not in', self::filterShopsIds()],
        ];
        $order = [
            'id'                           => 'desc'
        ];

        // 先用原词匹配
        $whereOr = [['name|remark|content', 'like', "%{$keyword}%"]];
        $ids = Goods::where($baseWhere)
            ->where(function($q) use ($whereOr){ $q->whereOr($whereOr); })
            ->orderRaw('(sales_actual + sales_virtual) desc')
            ->orderRaw('(clicks + clicks_virtual) desc')
            ->order($order)
            ->limit($limit)
            ->column('id');

        // 若数量不足，尝试分词补齐
        if (count($ids) < $limit) {
            try {
                $words = Alisegment($keyword)['words'] ?? [];
            } catch (\Exception $e) {
                $words = [];
            }
            $or2 = [];
            foreach ($words as $w) {
                if (mb_strlen($w,'UTF-8') >= 2) {
                    $or2[] = ['name|remark|content', 'like', "%".trim($w)."%"];
                }
            }
            if (!empty($or2)) {
                $more = Goods::where($baseWhere)
                    ->where(function($q) use ($or2){ $q->whereOr($or2); })
                    ->order($order)
                    ->limit($limit)
                    ->column('id');
                $ids = array_values(array_unique(array_merge($ids, $more)));
            }
        }

        return array_slice($ids, 0, $limit);
    }


    /**
     * 合并和排序推荐结果
     * @param array $recommendations 所有推荐结果
     * @param array $userPreferences 用户偏好
     * @return array 最终排序的商品ID列表
     */
    private static function mergeAndRankRecommendations($recommendations, $userPreferences)
    {
        $finalScores = [];
        $categoryCount = [];
        $shopCount = [];

        // 合并权重
        foreach ($recommendations as $rec) {
            $goodsId = $rec['goods_id'];
            if (!isset($finalScores[$goodsId])) {
                $finalScores[$goodsId] = 0;
            }
            $finalScores[$goodsId] += $rec['weight'];
        }

        // 获取商品详细信息用于多样性检查
        if (!empty($finalScores)) {
            $goodsInfo = Goods::whereIn('id', array_keys($finalScores))
                ->field('id,third_cate_id,shop_id,sales_actual,sales_virtual,create_time')
                ->select()
                ->toArray();

            $goodsInfoMap = [];
            foreach ($goodsInfo as $goods) {
                $goodsInfoMap[$goods['id']] = $goods;
            }

            // 应用多样性策略和额外评分
            $adjustedScores = [];
            foreach ($finalScores as $goodsId => $score) {
                if (!isset($goodsInfoMap[$goodsId])) continue;

                $goods = $goodsInfoMap[$goodsId];
                $adjustedScore = $score;

                // 获取多样性控制配置
                $diversityConfig = Config::get('recommend.diversity_control', []);
                $maxSameCategory = $diversityConfig['max_same_category'] ?? 5;
                $maxSameShop = $diversityConfig['max_same_shop'] ?? 3;
                $categoryPenalty = $diversityConfig['category_penalty'] ?? 0.8;
                $shopPenalty = $diversityConfig['shop_penalty'] ?? 0.7;

                // 多样性惩罚：同一分类商品过多时降低权重
                $cateId = $goods['third_cate_id'];
                $categoryCount[$cateId] = ($categoryCount[$cateId] ?? 0) + 1;
                if ($categoryCount[$cateId] > $maxSameCategory) {
                    $adjustedScore *= $categoryPenalty;
                }

                // 多样性惩罚：同一店铺商品过多时降低权重
                $shopId = $goods['shop_id'];
                $shopCount[$shopId] = ($shopCount[$shopId] ?? 0) + 1;
                if ($shopCount[$shopId] > $maxSameShop) {
                    $adjustedScore *= $shopPenalty;
                }

                // 获取评分加成配置
                $scoreBonusConfig = Config::get('recommend.score_bonus', []);
                $salesFactor = $scoreBonusConfig['sales_factor'] ?? 1000;
                $maxSalesBonus = $scoreBonusConfig['max_sales_bonus'] ?? 0.2;
                $newProductBonus = $scoreBonusConfig['new_product_bonus'] ?? 0.1;
                $newProductDays = $scoreBonusConfig['new_product_days'] ?? 7;

                // 销量加成
                $salesBonus = min(($goods['sales_actual'] + $goods['sales_virtual']) / $salesFactor, $maxSalesBonus);
                $adjustedScore += $salesBonus;

                // 新品加成
                if ($goods['create_time'] > time() - $newProductDays * 24 * 3600) {
                    $adjustedScore += $newProductBonus;
                }

                // 添加轻微抖动，保持相关性的同时增加多样性
                $jitterConfig = Config::get('recommend.jitter', []);
                $jitterEnabled = $jitterConfig['enabled'] ?? true;
                $jitterRange = $jitterConfig['range'] ?? 0.05; // 默认±5%

                if ($jitterEnabled) {
                    // 使用用户ID和当前分钟作为随机种子，确保同一时段内稳定
                    $userId = $userPreferences['user_id'] ?? 0;
                    $seed = crc32($userId . date('YmdHi'));
                    mt_srand($seed);

                    // 生成 -jitterRange 到 +jitterRange 的抖动
                    $jitter = (mt_rand(-100, 100) / 100.0) * $jitterRange;
                    $adjustedScore = $adjustedScore * (1 + $jitter);
                }

                $adjustedScores[$goodsId] = $adjustedScore;
            }

            // 按调整后的分数排序
            arsort($adjustedScores);
            return array_keys($adjustedScores);
        }

        return [];
    }

    /**
     * 生成最终的排序字段
     * @param array $sortedGoodsIds 排序后的商品ID列表
     * @return string 排序字段
     */
    private static function generateSortField($sortedGoodsIds)
    {
        if (empty($sortedGoodsIds)) {
            return 'RAND()';
        }

        // 获取最终结果数量限制
        $finalLimit = Config::get('recommend.recommendation_limits.final_result', 100);
        $limitedIds = array_slice($sortedGoodsIds, 0, $finalLimit);

        return 'FIELD(id, ' . implode(',', $limitedIds) . ')';
    }

    /**
     * 根据用户输入生成候选词（基于词性）
     * @param string $input 用户输入
     * @param int $limit 返回数量
     * @return array
     */
    public static function getSuggestionsByTags(string $input, int $limit = 10): array
    {
        if (empty($input)) {
            return [];
        }

        // 查找匹配的分词
        $words = Db::name('word')->where('word', 'like', $input . '%')
            ->order('word', 'asc')
            ->limit(100)
            ->select()
            ->column('id');

        if (empty($words)) {
            return [];
        }

        // 获取相关商品

        $productWords = ProductWord::with(['word', 'goods'])
            ->whereIn('word_id', $words)
            ->limit(100)
            ->select();

        if (empty($productWords)) {
            return [];
        }

        // 组织数据
        $candidates = [];
        foreach ($productWords as $pw) {

            // 检查$pw->goods是否为Collection类型，如果是，则遍历获取每个模型的name属性
            $productName = '';
            foreach ($pw['goods'] as $item) {
                $productName = $item['name'];
                break; // 只取第一个商品名称
            }
            if ($pw['word'] instanceof \think\model\Collection) {
                $tags = $pw['word']->column('tags');
            } else {
                // 否则直接获取单个模型的name属性
                $tags = $pw['word']['tags'];
            }

            // 计算权重
            $weight = self::calculateWordWeight($tags);

            $matchType = self::getMatchType($productName, $input);

            $candidates[] = [
                'text' => $productName,
                'weight' => $weight,
                'match_type' => $matchType,
                'length' => mb_strlen($productName)
            ];
        }

        // 去重
        $candidates = array_unique($candidates, SORT_REGULAR);

        // 排序
        usort($candidates, function($a, $b) {
            // 先按匹配类型排序
            if ($a['match_type'] !== $b['match_type']) {
                return $a['match_type'] <=> $b['match_type'];
            }

            // 再按权重排序
            if ($a['weight'] !== $b['weight']) {
                return $b['weight'] <=> $a['weight'];
            }

            // 最后按长度排序
            return $a['length'] <=> $b['length'];
        });

        // 取前N个
        $result = array_slice($candidates, 0, $limit);

        // 只返回文本
        return array_column($result, 'text');
    }

    /**
         * 计算词性权重
         * @param array $tags
         * @return int
         */
        protected static function calculateWordWeight(array $tags): int
        {
            $weight = 0;
            foreach ($tags as $tag) {
                // 使用静态属性或类常量替代 $this，因为这是在静态方法中
                $weight += self::$tagWeights[$tag] ?? 0;
                // 或者如果 $tagWeights 是一个静态方法返回的数组，可以这样调用
                // $weight += YourClassName::getTagWeights()[$tag] ?? 0;
            }
            return $weight;
        }

    /**
     * 获取匹配类型
     * @param string $text
     * @param string $input
     * @return int
     */
    protected static function getMatchType(string $text, string $input): int
    {

        // 0: 开头匹配（最高优先级）
        if (mb_strpos($text, $input) === 0) {
            return 0;
        }

        // 1: 包含匹配（次优先级）
        if (mb_strpos($text, $input) !== false) {
            return 1;
        }

        // 2: 其他匹配（最低优先级）
        return 2;
    }
    /**
     * @notes 根据已选规格获取剩余可用规格及库存
     * @param int $goods_id 商品ID
     * @param array $selected_spec_values 已选择的规格值名称数组，例如：['红色', 'S']
     * @return array|false
     * <AUTHOR>
     * @date 2025/07/18
     */
    public static function getAvailableSpecStock($goods_id, $selected_spec_values)
    {
        try {
            // 1. 根据规格值名称查询规格详情
            $selected_details = Db::name('goods_spec_value')
                ->where('goods_id', $goods_id)
                ->whereIn('value', $selected_spec_values)
                ->select()
                ->toArray();

            // 校验选择的规格是否都有效
            if (count($selected_details) !== count($selected_spec_values)) {
                self::$error = '一个或多个规格值无效';
                return false;
            }
            $selected_value_ids = array_column($selected_details, 'id');
            $selected_spec_ids = array_unique(array_column($selected_details, 'spec_id'));

            // 2. 查找包含所有已选规格的SKU (goods_item)
            $query = GoodsItem::where('goods_id', $goods_id);
            foreach ($selected_value_ids as $vid) {
                $query->whereRaw("FIND_IN_SET(?, spec_value_ids)", [$vid]);
            }
            $compatible_items = $query->select()->toArray();

            // 3. 从兼容的SKU中，聚合所有其他可选规格的库存
            $available_specs_stock = [];
            foreach ($compatible_items as $item) {
                $item_spec_ids = explode(',', $item['spec_value_ids']);
                foreach ($item_spec_ids as $spec_value_id) {
                    // 只处理非已选的规格
                    if (!in_array($spec_value_id, $selected_value_ids)) {
                        if (!isset($available_specs_stock[$spec_value_id])) {
                            $available_specs_stock[$spec_value_id] = 0;
                        }
                        $available_specs_stock[$spec_value_id] += $item['stock'];
                    }
                }
            }

            if (empty($available_specs_stock)) {
                return []; // 没有其他可搭配的规格了
            }

            // 4. 获取这些可用规格的详细信息
            $available_spec_ids = array_keys($available_specs_stock);
            $spec_details = Db::name('goods_spec_value')->alias('gsv')
                ->join('goods_spec gs', 'gsv.spec_id = gs.id')
                ->whereIn('gsv.id', $available_spec_ids)
                ->field('gsv.id, gsv.value, gsv.image, gs.id as spec_id, gs.name as spec_name')
                ->select()
                ->toArray();

            // 5. 整理数据结构，按规格项分组
            $result = [];
            foreach ($spec_details as $detail) {
                $spec_id = $detail['spec_id'];
                $spec_name = $detail['spec_name'];

                // 如果某个规格项已经被用户选择，则不应再出现在返回结果中
                if (in_array($spec_id, $selected_spec_ids)) {
                    continue;
                }

                if (!isset($result[$spec_name])) {
                    $result[$spec_name] = [
                        'name' => $spec_name,
                        'values' => []
                    ];
                }

                $stock = $available_specs_stock[$detail['id']] ?? 0;
                $result[$spec_name]['values'][] = [
                    'id' => $detail['id'],
                    'value' => $detail['value'],
                    'image' => $detail['image'] ? UrlServer::getFileUrl($detail['image']) : '',
                    'stock' => $stock,
                    'is_stock_out' => $stock <= 0,
                ];
            }

            return array_values($result);

        } catch (\Exception $e) {
            self::$error = '查询库存失败: ' . $e->getMessage();
            return false;
        }
    }

    /**
     * 构建 MeiliSearch 过滤条件
     * 将 ThinkPHP 的 where 条件转换为 MeiliSearch 的 filter 语法
     *
     * @param array $where ThinkPHP where 条件数组
     * @return array MeiliSearch filter 条件数组
     */
    private static function buildMeiliSearchFilter(array $where): array
    {
        $filter = [];

        foreach ($where as $condition) {
            if (!is_array($condition) || count($condition) < 3) {
                continue;
            }

            $field = $condition[0];
            $operator = $condition[1];
            $value = $condition[2];

            // 处理不同的操作符
            switch ($operator) {
                case '=':
                    if (is_numeric($value)) {
                        $filter[] = "{$field} = {$value}";
                    } else {
                        $filter[] = "{$field} = \"{$value}\"";
                    }
                    break;

                case 'in':
                    if (is_array($value) && !empty($value)) {
                        $valueStr = implode(',', array_map(function($v) {
                            return is_numeric($v) ? $v : "\"{$v}\"";
                        }, $value));
                        $filter[] = "{$field} IN [{$valueStr}]";
                    }
                    break;

                case 'not in':
                    if (is_array($value) && !empty($value)) {
                        $valueStr = implode(',', array_map(function($v) {
                            return is_numeric($v) ? $v : "\"{$v}\"";
                        }, $value));
                        $filter[] = "{$field} NOT IN [{$valueStr}]";
                    }
                    break;

                case '>':
                    $filter[] = "{$field} > {$value}";
                    break;

                case '>=':
                    $filter[] = "{$field} >= {$value}";
                    break;

                case '<':
                    $filter[] = "{$field} < {$value}";
                    break;

                case '<=':
                    $filter[] = "{$field} <= {$value}";
                    break;

                case '!=':
                case '<>':
                    if (is_numeric($value)) {
                        $filter[] = "{$field} != {$value}";
                    } else {
                        $filter[] = "{$field} != \"{$value}\"";
                    }
                    break;
            }
        }

        return $filter;
    }

    /**
     * 构建 MeiliSearch 排序选项
     * 将 ThinkPHP 的排序参数和用户排序选择转换为 MeiliSearch 的 sort 语法
     *
     * @param array $get 用户请求参数
     * @param array $order ThinkPHP order 条件数组
     * @return array MeiliSearch sort 选项数组
     */
    private static function buildMeiliSearchSort(array $get, array $order): array
    {
        $sortOptions = [];

        // 处理用户明确的排序选择（优先级最高）
        if (!empty($get['sort_by_price'])) {
            $direction = strtolower(trim($get['sort_by_price'])) === 'desc' ? 'desc' : 'asc';
            $sortOptions[] = "min_price:{$direction}";
        }

        if (!empty($get['sort_by_sales'])) {
            $direction = strtolower(trim($get['sort_by_sales'])) === 'desc' ? 'desc' : 'asc';
            // 注意：MeiliSearch 中需要预先计算好 sales_total 字段
            $sortOptions[] = "sales_total:{$direction}";
        }

        if (!empty($get['sort_by_create'])) {
            $direction = strtolower(trim($get['sort_by_create'])) === 'desc' ? 'desc' : 'asc';
            $sortOptions[] = "create_time:{$direction}";
        }

        // 如果没有用户明确排序，使用默认排序
        if (empty($sortOptions) && !empty($order)) {
            foreach ($order as $field => $direction) {
                $direction = strtolower($direction) === 'desc' ? 'desc' : 'asc';

                // 映射 ThinkPHP 字段到 MeiliSearch 字段
                $meiliField = self::mapFieldToMeiliSearch($field);
                if ($meiliField) {
                    $sortOptions[] = "{$meiliField}:{$direction}";
                }
            }
        }

        // 如果仍然没有排序选项，使用默认的相关性排序（MeiliSearch 内置）
        // 不需要显式指定，MeiliSearch 会自动按相关性排序

        return $sortOptions;
    }

    /**
     * 映射 ThinkPHP 字段名到 MeiliSearch 字段名
     *
     * @param string $field ThinkPHP 字段名
     * @return string|null MeiliSearch 字段名，如果不支持则返回 null
     */
    private static function mapFieldToMeiliSearch(string $field): ?string
    {
        $fieldMapping = [
            'sales_total' => 'sales_total',
            'sales_actual' => 'sales_actual',
            'sales_virtual' => 'sales_virtual',
            'min_price' => 'min_price',
            'market_price' => 'market_price',
            'create_time' => 'create_time',
            'sort_weight' => 'sort_weight',
            'clicks' => 'clicks',
            'clicks_virtual' => 'clicks_virtual',
            'id' => 'id'
        ];

        return $fieldMapping[$field] ?? null;
    }

    /**
     * 获取实时关联推荐商品
     * 基于当前搜索词使用 MeiliSearch 召回强相关商品，用于实时置顶
     *
     * @param string $keyword 当前搜索关键词
     * @param int $limit 推荐数量限制
     * @param array $validShopIds 有效店铺ID列表
     * @return array 推荐商品ID列表
     */
    private static function getRealtimeAssociatedRecommendations(string $keyword, int $limit = 60, array $validShopIds = []): array
    {
        if (empty($keyword)) {
            return [];
        }

        try {
            // 初始化MeiliSearch客户端
            $meili = new \app\common\library\MeiliSearch();

            // 测试连接
            if (!$meili->testConnection()) {
                \think\facade\Log::warning('MeiliSearch连接失败，实时关联推荐回退到SQL');
                return self::getRealtimeAssociatedRecommendationsFallback($keyword, $limit, $validShopIds);
            }

            // 构建基础过滤条件
            $baseFilter = [
                'del = 0',
                'status = 1',
                'audit_status = 1',
                'join_jc = 0'
            ];

            // 使用传递的有效店铺ID列表
            if (!empty($validShopIds)) {
                $shopIdStr = implode(',', $validShopIds);
                $baseFilter[] = "shop_id IN [{$shopIdStr}]";
            } else {
                // 如果没有传递有效店铺ID，使用原来的过滤逻辑
                $invalidShopIds = self::filterShopsIds();
                if (!empty($invalidShopIds)) {
                    $shopIdStr = implode(',', $invalidShopIds);
                    $baseFilter[] = "shop_id NOT IN [{$shopIdStr}]";
                }
            }

            // 构建搜索选项
            $searchOptions = [
                'limit' => $limit,
                'offset' => 0,
                'attributesToRetrieve' => ['id'],
                'filter' => implode(' AND ', $baseFilter),
                'matchingStrategy' => 'all',
                'attributesToSearchOn' => ['name', 'remark', 'content', 'category_path', 'brand_name', 'tags'],
                // 提升相关性排序
                'sort' => ['sales_total:desc', 'clicks:desc']
            ];

            $goodsIds = [];

            // 1. 短语搜索（最高相关性）
            $phraseQuery = '"' . $keyword . '"';
            $results = $meili->advancedSearch('goods', $phraseQuery, $searchOptions);
            if (isset($results['hits']) && !empty($results['hits'])) {
                $goodsIds = array_merge($goodsIds, array_column($results['hits'], 'id'));
            }

            // 2. 如果短语搜索结果不足，进行普通搜索
            if (count($goodsIds) < $limit) {
                $searchOptions['limit'] = $limit - count($goodsIds);
                $results = $meili->advancedSearch('goods', $keyword, $searchOptions);
                if (isset($results['hits']) && !empty($results['hits'])) {
                    $newIds = array_column($results['hits'], 'id');
                    $goodsIds = array_merge($goodsIds, array_diff($newIds, $goodsIds));
                }
            }

            // 3. 如果还不足，尝试分词搜索
            if (count($goodsIds) < $limit) {
                try {
                    $words = Alisegment($keyword);
                    if (!empty($words['words']) && is_array($words['words'])) {
                        $segmentedKeyword = implode(' ', $words['words']);
                        $searchOptions['limit'] = $limit - count($goodsIds);
                        $results = $meili->advancedSearch('goods', $segmentedKeyword, $searchOptions);
                        if (isset($results['hits']) && !empty($results['hits'])) {
                            $newIds = array_column($results['hits'], 'id');
                            $goodsIds = array_merge($goodsIds, array_diff($newIds, $goodsIds));
                        }
                    }
                } catch (\Exception $e) {
                    \think\facade\Log::warning('分词失败，跳过分词搜索: ' . $e->getMessage());
                }
            }

            // 去重并限制数量
            $goodsIds = array_unique($goodsIds);
            return array_slice($goodsIds, 0, $limit);

        } catch (\Exception $e) {
            \think\facade\Log::error('实时关联推荐MeiliSearch异常: ' . $e->getMessage());
            return self::getRealtimeAssociatedRecommendationsFallback($keyword, $limit, $validShopIds);
        }
    }

    /**
     * 实时关联推荐的SQL兜底方法
     *
     * @param string $keyword 搜索关键词
     * @param int $limit 推荐数量限制
     * @param array $validShopIds 有效店铺ID列表
     * @return array 推荐商品ID列表
     */
    private static function getRealtimeAssociatedRecommendationsFallback(string $keyword, int $limit = 60, array $validShopIds = []): array
    {
        $baseWhere = [
            ['del', '=', GoodsEnum::DEL_NORMAL],
            ['status', '=', GoodsEnum::STATUS_SHELVES],
            ['audit_status', '=', GoodsEnum::AUDIT_STATUS_OK],
            ['join_jc', '=', 0]
        ];

        // 使用传递的有效店铺ID列表
        if (!empty($validShopIds)) {
            $baseWhere[] = ['shop_id', 'in', $validShopIds];
        } else {
            // 如果没有传递有效店铺ID，使用原来的过滤逻辑
            $baseWhere[] = ['shop_id', 'not in', self::filterShopsIds()];
        }

        // 构建搜索条件
        $whereOr = [['name|remark|content', 'like', "%{$keyword}%"]];

        // 尝试分词
        try {
            $words = Alisegment($keyword);
            if (!empty($words['words']) && is_array($words['words'])) {
                foreach ($words['words'] as $word) {
                    if (mb_strlen($word, 'UTF-8') >= 2) {
                        $whereOr[] = ['name|remark|content', 'like', "%{$word}%"];
                    }
                }
            }
        } catch (\Exception $e) {
            // 分词失败，使用原词
        }

        $goodsIds = Goods::where($baseWhere)
            ->where(function ($query) use ($whereOr) {
                $query->whereOr($whereOr);
            })
            ->order([
                'sales_actual' => 'desc',
                'clicks' => 'desc',
                'id' => 'desc'
            ])
            ->limit($limit)
            ->column('id');

        return $goodsIds;
    }
}
